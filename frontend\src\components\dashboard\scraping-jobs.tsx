import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import {
  AlertCircle,
  ArrowUpDown,
  Award,
  BarChart2,
  Briefcase,
  Building2,
  CalendarClock,
  CalendarDays,
  CheckCircle2,
  ChevronDown,
  ChevronRight,
  Clock,
  DollarSign,
  FileDown,
  FileSpreadsheet,
  FileText,
  Loader,
  Loader2,
  MapPin,
  MessageSquare,
  RefreshCw,
  Search,
  Star,
  Target,
  ThumbsDown,
  ThumbsUp,
  Timer,
  Trash2,
  Upload,
  Users,
  XCircle,
  Link,
  User,
} from "lucide-react";
import React, { useCallback, useState } from "react";
import { toast } from "sonner";

// API base URL - adjust this based on your backend URL
const API_BASE_URL = "http://localhost:8080";

interface Project {
  name: string;
  url: string;
  clientName: string;
  industry: string;
  platform: string;
  techUsed: string[];
  keyFeatures: string[];
  focus: string;
  outcome: string;
  otherInfo: string;
  thumbnailUrl: string;
}

interface Product {
  name: string;
  logoUrl: string;
  type: string;
  platforms: string[];
  techUsed: string[];
  keyFeatures: string[];
  otherInfo: string;
}

interface Award {
  name: string;
  url: string;
  awardedBy: string;
  year: number;
  imageUrl: string;
  isVerified: boolean;
}

interface CompanyEntity {
  id: number;
  name: string;
  // Clutch fields
  clutchStatus: boolean;
  clutchSlug?: string;
  clutchProviderName?: string;
  clutchCompanyLogoURL?: string;
  clutchCompanyOverview?: string;
  clutchCompanyUrl?: string;
  clutchProfileUrl?: string;
  clutchTotalReviews?: number;
  clutchMinimumProjectSize?: number;
  clutchHourlyRateRange?: string;
  clutchCity?: string;
  clutchCountry?: string;
  clutchOverallRating?: string;
  clutchQualityRating?: string;
  clutchScheduleRating?: string;
  clutchCostRating?: string;
  clutchWillingToRefer?: boolean;
  clutchVerificationBadge?: boolean;
  clutchVerified?: boolean;
  clutchVerificationBadgeText?: string;
  clutchAllServices?: string;
  clutchAllIndustries?: string;
  clutchFoundedYear?: string;
  clutchCompanySize?: string;
  clutchSpecialties?: string;
  topMentions?: string;
  // Goodfirms fields
  goodfirmsStatus: boolean;
  goodfirmsProviderName?: string;
  goodfirmsProfileUrl?: string;
  goodfirmsRating?: number;
  goodfirmsReviewsCount?: number;
  goodfirmsLocation?: string;
  goodfirmsVerificationBadge?: boolean;
  goodfirmsClientLikes?: string;
  goodfirmsClientDislikes?: string;
  // Glassdoor fields
  glassdoorStatus: boolean;
  glassdoorRating?: number;
  glassdoorReviewsCount?: number;
  glassdoorEmployeeSatisfaction?: number;
  glassdoorPros?: string;
  glassdoorCons?: string;
  glassdoorCategoryRatings?: string;
  glassdoorRatingsDistribution?: string;
  // LinkedIn fields
  linkedinStatus: boolean;
  linkedinFollowers?: number;
  linkedinEmployeesCount?: number;
  linkedinIndustry?: string;
  // Website fields
  websiteStatus: boolean;
  websiteDescription?: string;
  websiteFocusStatement?: string;
  websiteFoundedYear?: string;
  websiteEmployeeSize?: string;
  websiteHeadquartersLocation?: string;
  websiteServices?: string;
  websiteIndustries?: string;
  websiteAliases?: string[];
  websiteFundingStatus?: string;
  websiteCultureDescription?: string;
  websiteOutcomeSummary?: string;
  websiteGlobalPresence?: string[];
  websiteFoundingStory?: string;
  websiteInitiatives?: string[];
  websiteProjects?: Project[];
  websiteProducts?: Product[];
  websiteAwards?: Award[];
  // General fields
  dataCompletenessScore?: number;
  lastScraped?: string;
}

interface Job {
  id: number;
  name: string;
  status: "QUEUE" | "SCHEDULED" | "IN_PROGRESS" | "SUCCESS" | "FAILED";
  createdAt: string;
  updatedAt: string;
  nextScheduledTime?: string;
  lastProcessedTime?: string;
  glassdoor?: string;
  clutch?: string;
  goodfirms?: string;
  linkedin?: string;
  website?: string;
  retryCount: number;
  maxRetries: number;
  createdBy?: string;
  lastModifiedBy?: string;
  companyEntity?: CompanyEntity;
  csvUploadId?: string;
}

interface CsvUpload {
  id: string;
  fileName: string;
  uploadedAt: string;
  totalJobs: number;
  completedJobs: number;
  failedJobs: number;
  inProgressJobs: number;
}

const statusConfig = {
  QUEUE: {
    icon: <Timer className="h-4 w-4 text-gray-500" />,
    label: "Queue",
    color: "bg-gray-100 text-gray-800",
  },
  SCHEDULED: {
    icon: <CalendarClock className="h-4 w-4 text-blue-500" />,
    label: "Scheduled",
    color: "bg-blue-100 text-blue-800",
  },
  IN_PROGRESS: {
    icon: <Loader className="h-4 w-4 animate-spin text-yellow-500" />,
    label: "In Progress",
    color: "bg-yellow-100 text-yellow-800",
  },
  SUCCESS: {
    icon: <CheckCircle2 className="h-4 w-4 text-green-500" />,
    label: "Success",
    color: "bg-green-100 text-green-800",
  },
  FAILED: {
    icon: <XCircle className="h-4 w-4 text-red-500" />,
    label: "Failed",
    color: "bg-red-100 text-red-800",
  },
};

const formatDate = (dateString?: string) => {
  if (!dateString) return "N/A";
  try {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    }).format(new Date(dateString));
  } catch (error) {
    console.error("Error formatting date:", dateString, error);
    return "Invalid Date";
  }
};

// Helper function to format JSON string
const formatJsonString = (jsonString: string | undefined): string => {
  if (!jsonString) return "";
  try {
    // If it's a JSON string, parse and stringify it with formatting
    const parsed = JSON.parse(jsonString);
    return JSON.stringify(parsed, null, 2);
  } catch {
    // If it's not JSON, return as is
    return jsonString;
  }
};

interface JsonViewProps {
  label: string;
  content?: string;
  icon?: React.ReactNode;
}

const JsonView = React.memo(function JsonView({
  label,
  content,
  icon,
}: JsonViewProps) {
  if (!content) return null;

  return (
    <div className="space-y-1">
      <div className="flex items-center gap-2 text-sm font-medium">
        {icon}
        <span>{label}</span>
      </div>
      <Textarea
        value={formatJsonString(content)}
        disabled
        className="font-mono text-xs h-[100px] resize-none bg-muted/50"
      />
    </div>
  );
});

const ScraperStatusBadge = React.memo(function ScraperStatusBadge({
  status,
  source,
  jobStatus,
}: {
  status: boolean | null | undefined;
  source: string;
  jobStatus: string;
}) {
  // If job is in PROCESSING or SCHEDULED state, show pending status
  if (
    jobStatus === "IN_PROGRESS" ||
    jobStatus === "SCHEDULED" ||
    jobStatus === "QUEUE"
  ) {
    return (
      <Badge variant="outline" className="gap-1">
        <Clock className="h-3 w-3 text-blue-500" />
        {source} ({jobStatus === "IN_PROGRESS" ? "Processing" : "Awaiting"})
      </Badge>
    );
  }

  // For completed jobs, show actual scraper status
  if (status === true) {
    return (
      <Badge variant="default" className="bg-green-500 gap-1">
        <CheckCircle2 className="h-3 w-3" />
        {source} (Done)
      </Badge>
    );
  }

  if (status === false) {
    return (
      <Badge variant="destructive" className="gap-1">
        <XCircle className="h-3 w-3" />
        {source} (Failed)
      </Badge>
    );
  }

  // Default case (should not happen, but just in case)
  return (
    <Badge variant="outline" className="gap-1">
      <Clock className="h-3 w-3 text-blue-500" />
      {source} (Pending)
    </Badge>
  );
});

const CompanyDetails = ({ company, jobStatus }: { company: CompanyEntity, jobStatus: string }) => {
  if (!company) return null;

  const renderValue = (value: any) => {
    if (value === undefined || value === null || value === '') return 'N/A';
    if (typeof value === 'boolean') return value ? 'Yes' : 'No';
    if (typeof value === 'number') return value.toLocaleString();
    if (Array.isArray(value)) {
      if (value.length === 0) return 'N/A';
      if (typeof value[0] === 'string') return value.join(', ');
      return value.map((item, index) => (
        <div key={index} className="mb-2 p-2 bg-gray-50 rounded">
          {Object.entries(item).map(([key, val]) => (
            <div key={key} className="text-sm">
              <span className="font-medium">{key}: </span>
              {Array.isArray(val) ? val.join(', ') : String(val)}
            </div>
          ))}
        </div>
      ));
    }
    return value;
  };

  const renderField = (label: string, value: any) => (
    <div className="flex items-start py-2 border-b last:border-b-0">
      <span className="w-64 flex-shrink-0 font-medium text-sm text-gray-600">{label}:</span>
      <span className="flex-1 min-w-0 text-sm break-words whitespace-pre-wrap overflow-auto max-h-48 pr-4">
        {renderValue(value)}
      </span>
    </div>
  );

  const renderSourceStatus = (source: string, status: string | boolean | undefined) => {
    let icon, color, label;
    
    if (status === "SUCCESS" || status === true) {
      icon = <CheckCircle2 className="h-4 w-4" />;
      color = "bg-green-100 text-green-800";
      label = "Success";
    } else if (status === "FAILED" || status === false) {
      icon = <XCircle className="h-4 w-4" />;
      color = "bg-red-100 text-red-800";
      label = "Failed";
    } else if (status === "IN_PROGRESS") {
      icon = <Loader2 className="h-4 w-4 animate-spin" />;
      color = "bg-yellow-100 text-yellow-800";
      label = "In Progress";
    } else {
      icon = <Clock className="h-4 w-4" />;
      color = "bg-gray-100 text-gray-800";
      label = "Awaiting";
    }

    return (
      <Badge variant="secondary" className={`${color} gap-1 items-center`}>
        {icon}
        <span>{source}</span>
        <span className="font-medium">{label}</span>
      </Badge>
    );
  };

  return (
    <div className="space-y-6 p-4 max-w-full overflow-hidden">
      {/* Source Status Badges */}
      <div className="flex flex-wrap gap-2 mb-4">
        {renderSourceStatus("Clutch", company.clutchStatus)}
        {renderSourceStatus("Goodfirms", company.goodfirmsStatus)}
        {renderSourceStatus("Glassdoor", company.glassdoorStatus)}
        {renderSourceStatus("LinkedIn", company.linkedinStatus)}
        {renderSourceStatus("Website", company.websiteStatus)}
      </div>

      {/* Data Completeness Score */}
      {company.dataCompletenessScore !== undefined && (
        <div className="flex items-center gap-4 mb-6">
          <div className="flex-1">
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm font-medium">Data Completeness</span>
              <span className="text-sm font-medium">{Math.round(company.dataCompletenessScore * 100)}%</span>
            </div>
            <Progress value={company.dataCompletenessScore * 100} className="h-2" />
          </div>
        </div>
      )}

      <div className="rounded-lg border p-4 overflow-auto">
        {/* Basic Info */}
        <div className="mb-4">
          <h3 className="text-sm font-semibold mb-2">Basic Information</h3>
          {renderField('Company ID', company.id)}
          {renderField('Company Name', company.name)}
          {renderField('Last Scraped', company.lastScraped)}
        </div>

        {/* Clutch Data */}
        <div className="mb-4">
          <h3 className="text-sm font-semibold mb-2">Clutch Data</h3>
          {renderField('Clutch Status', company.clutchStatus)}
          {renderField('Overall Rating', company.clutchOverallRating)}
          {renderField('Reviews Count', company.clutchTotalReviews)}
          {renderField('Location', `${company.clutchCity || ''}, ${company.clutchCountry || ''}`.replace(/(^,\s*)|(\s*,\s*$)/g, ''))}
          {renderField('Company Size', company.clutchCompanySize)}
          {renderField('Founded Year', company.clutchFoundedYear)}
          {renderField('Hourly Rate', company.clutchHourlyRateRange)}
          {renderField('Min Project Size', company.clutchMinimumProjectSize ? `$${company.clutchMinimumProjectSize}+` : 'N/A')}
          {renderField('Company URL', company.clutchCompanyUrl)}
          {renderField('Company Logo', company.clutchCompanyLogoURL)}
          {renderField('Company Overview', company.clutchCompanyOverview)}
          {renderField('Services', company.clutchAllServices)}
          {renderField('Industries', company.clutchAllIndustries)}
          {renderField('Specialties', company.clutchSpecialties)}
          {renderField('Provider Name', company.clutchProviderName)}
          {renderField('Profile URL', company.clutchProfileUrl)}
          {renderField('Verification Badge', company.clutchVerificationBadge)}
          {renderField('Verification Status', company.clutchVerified)}
          {renderField('Verification Text', company.clutchVerificationBadgeText)}
          {renderField('Quality Rating', company.clutchQualityRating)}
          {renderField('Schedule Rating', company.clutchScheduleRating)}
          {renderField('Cost Rating', company.clutchCostRating)}
          {renderField('Willing to Refer', company.clutchWillingToRefer)}
          {renderField('Top Mentions', company.topMentions)}
        </div>

        {/* Goodfirms Data */}
        <div className="mb-4">
          <h3 className="text-sm font-semibold mb-2">Goodfirms Data</h3>
          {renderField('Goodfirms Status', company.goodfirmsStatus)}
          {renderField('Provider Name', company.goodfirmsProviderName)}
          {renderField('Profile URL', company.goodfirmsProfileUrl)}
          {renderField('Rating', company.goodfirmsRating)}
          {renderField('Reviews Count', company.goodfirmsReviewsCount)}
          {renderField('Location', company.goodfirmsLocation)}
          {renderField('Verification Badge', company.goodfirmsVerificationBadge)}
          {renderField('Client Likes', company.goodfirmsClientLikes)}
          {renderField('Client Dislikes', company.goodfirmsClientDislikes)}
        </div>

        {/* Glassdoor Data */}
        <div className="mb-4">
          <h3 className="text-sm font-semibold mb-2">Glassdoor Data</h3>
          {renderField('Glassdoor Status', company.glassdoorStatus)}
          {renderField('Rating', company.glassdoorRating)}
          {renderField('Reviews Count', company.glassdoorReviewsCount)}
          {renderField('Employee Satisfaction', company.glassdoorEmployeeSatisfaction)}
          {renderField('Category Ratings', company.glassdoorCategoryRatings)}
          {renderField('Ratings Distribution', company.glassdoorRatingsDistribution)}
          {renderField('Pros', company.glassdoorPros)}
          {renderField('Cons', company.glassdoorCons)}
        </div>

        {/* LinkedIn Data */}
        <div className="mb-4">
          <h3 className="text-sm font-semibold mb-2">LinkedIn Data</h3>
          {renderField('LinkedIn Status', company.linkedinStatus)}
          {renderField('Followers', company.linkedinFollowers)}
          {renderField('Employees Count', company.linkedinEmployeesCount)}
          {renderField('Industry', company.linkedinIndustry)}
        </div>

        {/* Website Data */}
        <div className="mb-4">
          <h3 className="text-sm font-semibold mb-2">Website Data</h3>
          {renderField('Status', company.websiteStatus)}
          {renderField('Description', company.websiteDescription)}
          {renderField('Focus Statement', company.websiteFocusStatement)}
          {renderField('Founded Year', company.websiteFoundedYear)}
          {renderField('Employee Size', company.websiteEmployeeSize)}
          {renderField('HQ Location', company.websiteHeadquartersLocation)}
          {renderField('Services', company.websiteServices)}
          {renderField('Industries', company.websiteIndustries)}
          {renderField('Aliases', company.websiteAliases)}
          {renderField('Funding Status', company.websiteFundingStatus)}
          {renderField('Culture Description', company.websiteCultureDescription)}
          {renderField('Outcome Summary', company.websiteOutcomeSummary)}
          {renderField('Global Presence', company.websiteGlobalPresence)}
          {renderField('Founding Story', company.websiteFoundingStory)}
          {renderField('Initiatives', company.websiteInitiatives)}
          
          {/* Projects Section */}
          <div className="mt-4">
            <h4 className="text-sm font-medium mb-2">Projects</h4>
            {renderField('Projects', company.websiteProjects)}
          </div>

          {/* Products Section */}
          <div className="mt-4">
            <h4 className="text-sm font-medium mb-2">Products</h4>
            {renderField('Products', company.websiteProducts)}
          </div>

          {/* Awards Section */}
          <div className="mt-4">
            <h4 className="text-sm font-medium mb-2">Awards</h4>
            {renderField('Awards', company.websiteAwards)}
          </div>
        </div>
      </div>
    </div>
  );
};

const JobStatusDisplay = ({ job }: { job: Job }) => {
  const { status, companyEntity } = job;

  if (
    status === "QUEUE" ||
    status === "SCHEDULED" ||
    status === "IN_PROGRESS"
  ) {
    return (
      <div className="flex items-center gap-2">
        {statusConfig[status]?.icon}
        <Badge className={statusConfig[status]?.color}>
          {statusConfig[status]?.label || status}
        </Badge>
      </div>
    );
  }

  if (companyEntity) {
    const scrapers = [
      companyEntity.clutchStatus,
      companyEntity.goodfirmsStatus,
      companyEntity.glassdoorStatus,
      companyEntity.linkedinStatus,
      companyEntity.websiteStatus,
    ];
    const successCount = scrapers.filter((s) => s === true).length;
    const failedCount = scrapers.filter((s) => s === false).length;

    const statusBadges = [];
    if (successCount > 0) {
      statusBadges.push(
        <Badge
          key="success"
          variant="default"
          className="bg-green-100 text-green-800 gap-1"
        >
          <CheckCircle2 className="h-4 w-4 text-green-500" /> {successCount}
        </Badge>
      );
    }
    if (failedCount > 0) {
      statusBadges.push(
        <Badge key="failed" variant="destructive" className="gap-1">
          <XCircle className="h-4 w-4 text-white" /> {failedCount}
        </Badge>
      );
    }

    if (statusBadges.length > 0) {
      return (
        <div className="flex flex-row gap-1 items-start">{statusBadges}</div>
      );
    }
  }

  return (
    <div className="flex items-center gap-2">
      {statusConfig[status]?.icon}
      <Badge className={statusConfig[status]?.color}>
        {statusConfig[status]?.label || status}
      </Badge>
    </div>
  );
};

// Helper function to flatten nested objects for CSV export
const flattenObject = (obj: any, prefix = ''): Record<string, string> => {
  const flattened: Record<string, string> = {};

  for (const key in obj) {
    if (obj[key] === null || obj[key] === undefined) continue;

    const value = obj[key];
    const newKey = prefix ? `${prefix}_${key}` : key;

    if (Array.isArray(value)) {
      if (value.length === 0) continue;
      if (typeof value[0] === 'object') {
        // For arrays of objects (like projects, products, awards)
        value.forEach((item, index) => {
          const flatItem = flattenObject(item, `${newKey}_${index + 1}`);
          Object.assign(flattened, flatItem);
        });
      } else {
        // For arrays of primitives
        flattened[newKey] = value.join(', ');
      }
    } else if (typeof value === 'object') {
      Object.assign(flattened, flattenObject(value, newKey));
    } else {
      flattened[newKey] = String(value);
    }
  }

  return flattened;
};

// Helper function to convert data to CSV
const convertToCSV = (data: any[]): string => {
  if (data.length === 0) return '';

  // Flatten all objects to get all possible headers
  const flattenedData = data.map(item => flattenObject(item));
  const headers = Array.from(new Set(flattenedData.flatMap(obj => Object.keys(obj))));

  const csvRows = [
    headers.join(','), // Header row
    ...flattenedData.map(obj =>
      headers.map(header => {
        const value = obj[header] || '';
        // Escape commas and quotes in the value
        return `"${value.replace(/"/g, '""')}"`;
      }).join(',')
    )
  ];

  return csvRows.join('\n');
};

// Helper function to download CSV
const downloadCSV = (csvContent: string, fileName: string) => {
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', fileName);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

const CsvUploadCard = React.memo(function CsvUploadCard({
  upload,
  onDownload,
  onToggle,
  isExpanded,
  jobs,
}: {
  upload: CsvUpload;
  onDownload: () => void;
  onToggle: () => void;
  isExpanded: boolean;
  jobs: Job[];
}) {
  const [expandedJobs, setExpandedJobs] = useState<Set<number>>(new Set());
  const totalProgress = Math.round(
    (upload.completedJobs / upload.totalJobs) * 100
  );
  const hasFailures = upload.failedJobs > 0;
  const isComplete = upload.completedJobs === upload.totalJobs;
  const inProgress = upload.inProgressJobs > 0;

  const toggleJob = (jobId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    const newExpandedJobs = new Set(expandedJobs);
    if (newExpandedJobs.has(jobId)) {
      newExpandedJobs.delete(jobId);
    } else {
      newExpandedJobs.add(jobId);
    }
    setExpandedJobs(newExpandedJobs);
  };

  const handleExportData = () => {
    const exportData = jobs.map(job => {
      const company = job.companyEntity;
      if (!company) return null;

      return {
        job_id: job.id,
        job_name: job.name,
        job_status: job.status,
        job_created_at: job.createdAt,
        job_updated_at: job.updatedAt,
        job_next_scheduled_time: job.nextScheduledTime,
        job_last_processed_time: job.lastProcessedTime,
        job_retry_count: job.retryCount,
        job_max_retries: job.maxRetries,
        job_created_by: job.createdBy,
        job_last_modified_by: job.lastModifiedBy,
        // Company data
        company_id: company.id,
        company_name: company.name,
        data_completeness_score: company.dataCompletenessScore,
        last_scraped: company.lastScraped,
        // Clutch data
        clutch_status: company.clutchStatus,
        clutch_provider_name: company.clutchProviderName,
        // ... all other Clutch fields ...
        // Goodfirms data
        goodfirms_status: company.goodfirmsStatus,
        goodfirms_provider_name: company.goodfirmsProviderName,
        // ... all other Goodfirms fields ...
        // Glassdoor data
        glassdoor_status: company.glassdoorStatus,
        glassdoor_rating: company.glassdoorRating,
        // ... all other Glassdoor fields ...
        // LinkedIn data
        linkedin_status: company.linkedinStatus,
        linkedin_followers: company.linkedinFollowers,
        // ... all other LinkedIn fields ...
        // Website data
        website_status: company.websiteStatus,
        website_description: company.websiteDescription,
        website_focus_statement: company.websiteFocusStatement,
        website_founded_year: company.websiteFoundedYear,
        website_employee_size: company.websiteEmployeeSize,
        website_headquarters_location: company.websiteHeadquartersLocation,
        website_services: company.websiteServices,
        website_industries: company.websiteIndustries,
        website_aliases: company.websiteAliases,
        website_funding_status: company.websiteFundingStatus,
        website_culture_description: company.websiteCultureDescription,
        website_outcome_summary: company.websiteOutcomeSummary,
        website_global_presence: company.websiteGlobalPresence,
        website_founding_story: company.websiteFoundingStory,
        website_initiatives: company.websiteInitiatives,
        // Projects, Products, and Awards will be flattened by flattenObject
        website_projects: company.websiteProjects,
        website_products: company.websiteProducts,
        website_awards: company.websiteAwards,
      };
    }).filter(Boolean);

    const csvContent = convertToCSV(exportData);
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    downloadCSV(csvContent, `scraping-data-${timestamp}.csv`);
  };

  return (
    <Card>
      <CardHeader className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileSpreadsheet className="h-5 w-5 text-gray-500" />
            <div>
              <CardTitle className="text-base">{upload.fileName}</CardTitle>
              <CardDescription>
                Uploaded {formatDate(upload.uploadedAt)}
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              className="gap-2"
              onClick={handleExportData}
            >
              <FileDown className="h-4 w-4" />
              Export CSV
            </Button>
            <Button
              variant={isComplete ? "default" : "outline"}
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onDownload();
              }}
              disabled={!isComplete}
              className="h-8 px-3 text-xs"
            >
              <FileDown className="h-3 w-3 mr-1.5" />
              {isComplete ? "Download" : "Processing"}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1.5">
              <Clock className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground">Total</span>
              <span className="font-medium">{upload.totalJobs}</span>
            </div>
            <div className="flex items-center gap-1.5">
              <CheckCircle2 className="h-3 w-3 text-green-500" />
              <span className="text-green-600">Done</span>
              <span className="font-medium text-green-600">
                {upload.completedJobs}
              </span>
            </div>
            {upload.inProgressJobs > 0 && (
              <div className="flex items-center gap-1.5">
                <Loader className="h-3 w-3 text-blue-500 animate-spin" />
                <span className="text-blue-600">Active</span>
                <span className="font-medium text-blue-600">
                  {upload.inProgressJobs}
                </span>
              </div>
            )}
            {hasFailures && (
              <div className="flex items-center gap-1.5">
                <XCircle className="h-3 w-3 text-red-500" />
                <span className="text-red-600">Failed</span>
                <span className="font-medium text-red-600">
                  {upload.failedJobs}
                </span>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            {inProgress && (
              <span className="text-muted-foreground flex items-center gap-1">
                <Loader className="h-3 w-3 animate-spin" />
                Processing
              </span>
            )}
            <Button
              variant="ghost"
              size="sm"
              className="h-6 px-2 text-xs"
              onClick={onToggle}
            >
              {isExpanded ? (
                <>
                  <ChevronDown className="h-3 w-3 mr-1" />
                  Hide
                </>
              ) : (
                <>
                  <ChevronRight className="h-3 w-3 mr-1" />
                  Details
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Error Alert - Compact */}
        {hasFailures && !isExpanded && (
          <div className="flex items-center gap-1 text-xs text-red-500 mt-2 p-2 bg-red-50 rounded-md">
            <AlertCircle className="h-3 w-3" />
            <span>{upload.failedJobs} failed</span>
          </div>
        )}
      </CardContent>

      {isExpanded && (
        <div className="border-t bg-gray-50/50">
          <div className="p-4">
            <Table>
              <TableHeader>
                <TableRow className="hover:bg-transparent">
                  <TableHead className="h-8 text-xs">Company</TableHead>
                  <TableHead className="h-8 text-xs">Status</TableHead>
                  <TableHead className="h-8 text-xs">Queued</TableHead>
                  <TableHead className="h-8 text-xs">Scheduled</TableHead>
                  <TableHead className="h-8 text-xs">Processed</TableHead>
                  <TableHead className="h-8 w-[40px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {jobs.map((job) => (
                  <React.Fragment key={job.id}>
                    <TableRow
                      className={cn(
                        "hover:bg-muted/50 h-10",
                        job.status === "FAILED" && "bg-red-50/50"
                      )}
                    >
                      <TableCell className="font-medium text-sm py-2 truncate max-w-[200px]">
                        {job.name}
                      </TableCell>
                      <TableCell className="py-2">
                        <JobStatusDisplay job={job} />
                      </TableCell>
                      <TableCell className="text-xs text-muted-foreground py-2">
                        {formatDate(job.createdAt)}
                      </TableCell>
                      <TableCell className="text-xs text-muted-foreground py-2">
                        {formatDate(job.nextScheduledTime)}
                      </TableCell>
                      <TableCell className="text-xs text-muted-foreground py-2">
                        {formatDate(job.lastProcessedTime)}
                      </TableCell>
                      <TableCell className="py-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={(e) => toggleJob(job.id, e)}
                        >
                          {expandedJobs.has(job.id) ? (
                            <ChevronDown className="h-3 w-3" />
                          ) : (
                            <ChevronRight className="h-3 w-3" />
                          )}
                        </Button>
                      </TableCell>
                    </TableRow>
                    {expandedJobs.has(job.id) && (
                      <TableRow>
                        <TableCell colSpan={6} className="p-0">
                          <div className="border-t">
                            {job.companyEntity ? (
                              <CompanyDetails
                                company={job.companyEntity}
                                jobStatus={job.status}
                              />
                            ) : (
                              <div className="p-6 text-center text-muted-foreground">
                                <Loader2 className="h-5 w-5 animate-spin mx-auto mb-2" />
                                <p className="text-sm">
                                  Scraping in progress...
                                </p>
                              </div>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      )}
    </Card>
  );
});

export function ScrapingJobs() {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [csvUploads, setCsvUploads] = useState<CsvUpload[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [expandedUploads, setExpandedUploads] = useState<Set<string>>(
    new Set()
  );
  const [searchTerm, setSearchTerm] = useState("");

  const toggleUpload = (uploadId: string) => {
    const newExpandedUploads = new Set(expandedUploads);
    if (newExpandedUploads.has(uploadId)) {
      newExpandedUploads.delete(uploadId);
    } else {
      newExpandedUploads.add(uploadId);
    }
    setExpandedUploads(newExpandedUploads);
  };

  const fetchJobs = useCallback(async () => {
    setIsLoading(true);
    try {
      const responses = await Promise.all([
        fetch(`${API_BASE_URL}/api/scrapping/queue`),
        fetch(`${API_BASE_URL}/api/scrapping/scheduled`),
        fetch(`${API_BASE_URL}/api/scrapping/processing`),
        fetch(`${API_BASE_URL}/api/scrapping/completed`),
        fetch(`${API_BASE_URL}/api/scrapping/failed`),
      ]);

      // Check if any response is not ok
      for (const response of responses) {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
      }

      const [queued, scheduled, processing, completed, failed] =
        await Promise.all(responses.map((r) => r.json()));

      // Deduplicate jobs, keeping the most recent status
      const jobMap = new Map<number, Job>();
      const allJobs = [
        ...queued,
        ...scheduled,
        ...processing,
        ...completed,
        ...failed,
      ];

      allJobs.forEach((job) => {
        const existingJob = jobMap.get(job.id);
        if (
          !existingJob ||
          new Date(job.updatedAt) > new Date(existingJob.updatedAt)
        ) {
          jobMap.set(job.id, job);
        }
      });

      const sortedJobs = Array.from(jobMap.values())
        .sort(
          (a, b) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )
        .map((job) => ({
          ...job,
          // Mark as IN_PROGRESS if the job is currently being processed
          status: processing.some((p: Job) => p.id === job.id)
            ? "IN_PROGRESS"
            : job.status,
        }));

      setJobs(sortedJobs);
    } catch (error) {
      console.error("Error fetching jobs:", error);
      toast.error(
        error instanceof Error && error.message.includes("Failed to fetch")
          ? "Unable to connect to the server. Please make sure the backend is running."
          : "Failed to fetch jobs. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchCsvUploads = useCallback(async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/scrapping/csv-uploads`);
      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      const uploads = await response.json();
      const correctedUploads = uploads.map((upload: CsvUpload) => {
        const { totalJobs, completedJobs, failedJobs, inProgressJobs } = upload;
        if (inProgressJobs === 0 && totalJobs > completedJobs + failedJobs) {
          return {
            ...upload,
            inProgressJobs: totalJobs - completedJobs - failedJobs,
          };
        }
        return upload;
      });
      setCsvUploads(correctedUploads);
    } catch (error) {
      console.error("Error fetching CSV uploads:", error);
      toast.error("Failed to fetch CSV uploads");
    }
  }, []);

  const handleDownloadCsv = useCallback(
    async (csvUploadId: string, fileName: string) => {
      try {
        const response = await fetch(
          `${API_BASE_URL}/api/scrapping/download/${csvUploadId}`
        );
        if (!response.ok)
          throw new Error(`HTTP error! status: ${response.status}`);

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = fileName.replace(".csv", "_scraped.csv");
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        toast.success("CSV file downloaded successfully");
      } catch (error) {
        console.error("Error downloading CSV:", error);
        toast.error("Failed to download CSV file");
      }
    },
    []
  );

  const handleFileUpload = useCallback(
    async (file: File) => {
      const formData = new FormData();
      formData.append("file", file);

      try {
        const response = await fetch(`${API_BASE_URL}/api/scrapping/upload`, {
          method: "POST",
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => null);
          throw new Error(errorData?.error || "Upload failed");
        }

        const result = await response.json();
        toast.success(
          `CSV file uploaded successfully. ${result.successCount} jobs created.`
        );

        // Refresh both jobs and CSV uploads
        fetchJobs();
        fetchCsvUploads();
      } catch (error) {
        console.error("Error uploading file:", error);
        toast.error(
          error instanceof Error && error.message.includes("Failed to fetch")
            ? "Unable to connect to the server. Please make sure the backend is running."
            : "Failed to upload file. Please try again."
        );
      }
    },
    [fetchJobs, fetchCsvUploads]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragging(false);

      const file = e.dataTransfer.files[0];
      if (!file || !file.name.endsWith(".csv")) {
        toast.error("Please upload a valid CSV file.");
        return;
      }

      handleFileUpload(file);
    },
    [handleFileUpload]
  );

  const handleFileSelect = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (!file) return;

      if (!file.name.endsWith(".csv")) {
        toast.error("Please upload a valid CSV file.");
        return;
      }

      handleFileUpload(file);
    },
    [handleFileUpload]
  );

  React.useEffect(() => {
    fetchJobs();
    fetchCsvUploads();
    // Set up polling every 30 seconds
    const interval = setInterval(() => {
      fetchJobs();
      fetchCsvUploads();
    }, 30000);
    return () => clearInterval(interval);
  }, [fetchJobs, fetchCsvUploads]);

  const filteredCsvUploads = csvUploads.filter((upload) =>
    upload.fileName.toLowerCase().includes(searchTerm.trim().toLowerCase())
  );

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Scraping Jobs</CardTitle>
            <CardDescription>
              Upload CSV files to scrape company information from multiple
              sources.
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={fetchJobs}>
            <RefreshCw
              className={cn("h-4 w-4 mr-2", isLoading && "animate-spin")}
            />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div
          className={cn(
            "border-2 border-dashed rounded-lg p-4 text-center transition-colors",
            isDragging
              ? "border-primary bg-primary/5"
              : "border-muted-foreground/25",
            "hover:border-primary hover:bg-primary/5"
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="mx-auto flex max-w-[420px] flex-col items-center justify-center text-center">
            <Upload className="h-8 w-8 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold">Drop your CSV file here</h3>
            <p className="text-sm text-muted-foreground mb-4">
              or click to browse for a file
            </p>
            <input
              type="file"
              accept=".csv"
              className="hidden"
              onChange={handleFileSelect}
              id="file-upload"
            />
            <Button variant="secondary" asChild>
              <label htmlFor="file-upload" className="cursor-pointer">
                Choose File
              </label>
            </Button>
          </div>
        </div>

        {csvUploads.length > 0 && (
          <div className="mt-8 space-y-6">
            <div className="flex items-center justify-between gap-4">
              <h3 className="text-lg font-semibold">Recent Data Scraping</h3>
              <div className="relative w-full max-w-sm">
                <Search className="absolute left-2 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by filename..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="h-9 pl-8"
                />
              </div>
            </div>
            {filteredCsvUploads.map((upload) => (
              <CsvUploadCard
                key={upload.id}
                upload={upload}
                onDownload={() => handleDownloadCsv(upload.id, upload.fileName)}
                onToggle={() => toggleUpload(upload.id)}
                isExpanded={expandedUploads.has(upload.id)}
                jobs={jobs.filter((job) => job.csvUploadId === upload.id)}
              />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
