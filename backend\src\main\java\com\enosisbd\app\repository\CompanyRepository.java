package com.enosisbd.app.repository;

import com.enosisbd.app.entity.CompanyEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CompanyRepository extends JpaRepository<CompanyEntity, Long> {
    
    /**
     * Find company by name
     * @param name Company name
     * @return Optional Company
     */
    Optional<CompanyEntity> findByName(String name);
    
    /**
     * Find company by name (case insensitive)
     * @param name Company name
     * @return Optional Company
     */
    Optional<CompanyEntity> findByNameIgnoreCase(String name);
    
    /**
     * Find companies that have been successfully scraped from Clutch
     * @return List of companies with Clutch data
     */
    List<CompanyEntity> findByClutchStatusTrue();
    
    /**
     * Find companies that have been successfully scraped from Goodfirms
     * @return List of companies with Goodfirms data
     */
    List<CompanyEntity> findByGoodfirmsStatusTrue();
    
    /**
     * Find companies that have been successfully scraped from Glassdoor
     * @return List of companies with Glassdoor data
     */
    List<CompanyEntity> findByGlassdoorStatusTrue();
    
    /**
     * Find companies that have been successfully scraped from LinkedIn
     * @return List of companies with LinkedIn data
     */
    List<CompanyEntity> findByLinkedinStatusTrue();
    
    /**
     * Find companies that have been successfully scraped from Website
     * @return List of companies with Website data
     */
    List<CompanyEntity> findByWebsiteStatusTrue();
    
    /**
     * Find company by website name
     * @param websiteName Website name
     * @return Optional Company
     */
    Optional<CompanyEntity> findByWebsiteName(String websiteName);
    
    /**
     * Find company by website name (case insensitive)
     * @param websiteName Website name
     * @return Optional Company
     */
    Optional<CompanyEntity> findByWebsiteNameIgnoreCase(String websiteName);

} 