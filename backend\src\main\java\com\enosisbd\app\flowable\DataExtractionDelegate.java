package com.enosisbd.app.flowable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import com.enosisbd.app.entity.CompanyEntity;
import com.enosisbd.app.entity.ScrapeEntity;
import com.enosisbd.app.model.CompanyDataModel;
import com.enosisbd.app.model.Feature;
import com.enosisbd.app.model.Industry;
import com.enosisbd.app.model.ProductService;
import com.enosisbd.app.model.WebsiteInfo;
import com.enosisbd.app.repository.CompanyRepository;
import com.enosisbd.app.repository.ScrapeRepository;
import com.enosisbd.app.service.ClutchScraperService;
import com.enosisbd.app.service.GlassdoorScraperService;
import com.enosisbd.app.service.GoodfirmsScraperService;
import com.enosisbd.app.service.ScrapeSchedulerService;
import com.enosisbd.app.service.WebsiteScraperService;
import com.enosisbd.app.service.impl.website.WebsiteCrawlerServiceImpl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("dataExtractionDelegate")
@RequiredArgsConstructor
public class DataExtractionDelegate implements JavaDelegate {

    private final ScrapeRepository scrapeRepository;
    private final CompanyRepository companyRepository;
    private final ScrapeSchedulerService scrapeSchedulerService;
    private final ClutchScraperService clutchScraperService;
    private final GlassdoorScraperService glassdoorScraperService;
    private final GoodfirmsScraperService goodfirmsScraperService;
    private final WebsiteScraperService websiteScraperService;

    @Override
    public void execute(DelegateExecution execution) {
        Long scrapeEntityId = (Long) execution.getVariable("scrapeEntityId");

        log.info("Starting data extraction for scrape entity: {}", scrapeEntityId);

        try {
            // Get the scrape entity
            ScrapeEntity entity = scrapeRepository.findById(scrapeEntityId)
                    .orElseThrow(() -> new RuntimeException("Scrape entity not found: " + scrapeEntityId));

            // Perform data extraction
            performDataExtraction(entity, execution);

            // Set success flag for the workflow
            execution.setVariable("extractionSuccess", true);
            execution.setVariable("extractionError", null);

            log.info("Data extraction completed successfully for entity: {}", scrapeEntityId);

        } catch (Exception e) {
            log.error("Data extraction failed for entity {}: {}", scrapeEntityId, e.getMessage(), e);

            // Set failure flag for the workflow
            execution.setVariable("extractionSuccess", false);
            execution.setVariable("extractionError", e.getMessage());

            // Mark the entity as failed
            scrapeRepository.findById(scrapeEntityId).ifPresent(entity -> 
                scrapeSchedulerService.markAsFailed(entity, "Data extraction failed: " + e.getMessage())
            );

            throw new RuntimeException("Data extraction failed", e);
        }
    }

    /**
     * Perform the actual data extraction process
     */
    private void performDataExtraction(ScrapeEntity entity, DelegateExecution execution) {
        log.info("Extracting data for entity: {} with name: {}", entity.getId(), entity.getName());

        CompanyEntity companyEntity = entity.getCompanyEntity();
        if (companyEntity == null) {
            throw new RuntimeException("No company associated with scrape entity: " + entity.getId());
        }

        // Get URLs from execution variables
        String clutchUrl = (String) execution.getVariable("clutch");
        String goodfirmsUrl = (String) execution.getVariable("goodfirms");
        String glassdoorUrl = (String) execution.getVariable("glassdoor");
        String linkedinUrl = (String) execution.getVariable("linkedin");
        String websiteUrl = (String) execution.getVariable("website");

        log.info("Starting parallel data extraction for company: {}", companyEntity.getName());
        log.debug("URLs - Clutch: {}, Goodfirms: {}, Glassdoor: {}, LinkedIn: {}, Website: {}", 
            clutchUrl, goodfirmsUrl, glassdoorUrl, linkedinUrl, websiteUrl);

        // Create async tasks for different sources
        CompletableFuture<CompanyDataModel> futureClutch = null;
        CompletableFuture<CompanyDataModel> futureGoodfirms = null;
        CompletableFuture<CompanyDataModel> futureGlassdoor = null;
        CompletableFuture<CompanyDataModel> futureWebsite = null;

        // Start Clutch scraping if URL is provided
        if (clutchUrl != null && !clutchUrl.trim().isEmpty()) {
            futureClutch = CompletableFuture.supplyAsync(() -> {
                try {
                    log.info("Starting Clutch scraping for: {}", clutchUrl);
                    return clutchScraperService.scrapeFromClutch(clutchUrl);
                } catch (Exception e) {
                    log.error("Clutch scraping failed: {}", e.getMessage());
                    return CompanyDataModel.failed("Clutch", e.getMessage());
                }
            });
        }

        // Start Goodfirms scraping if URL is provided
        if (goodfirmsUrl != null && !goodfirmsUrl.trim().isEmpty()) {
            futureGoodfirms = CompletableFuture.supplyAsync(() -> {
                try {
                    log.info("Starting Goodfirms scraping for: {}", goodfirmsUrl);
                    return goodfirmsScraperService.scrapeFromGoodFirms(goodfirmsUrl);
                } catch (Exception e) {
                    log.error("Goodfirms scraping failed: {}", e.getMessage());
                    return CompanyDataModel.failed("Goodfirms", e.getMessage());
                }
            });
        }

        // Start Website scraping if URL is provided
        // if (websiteUrl != null && !websiteUrl.trim().isEmpty()) {
        //     futureWebsite = CompletableFuture.supplyAsync(() -> {
        //         try {
        //             log.info("Starting Website scraping for: {}", websiteUrl);
        //             return websiteScraperService.scrapeFromWebsite(websiteUrl);
        //         } catch (Exception e) {
        //             log.error("Website scraping failed: {}", e.getMessage());
        //             return CompanyDataModel.failed("Website", e.getMessage());
        //         }
        //     });
        // }

        // Start Glassdoor scraping if URL is provided
        // if (glassdoorUrl != null && !glassdoorUrl.trim().isEmpty()) {
        //     futureGlassdoor = CompletableFuture.supplyAsync(() -> {
        //         try {
        //             log.info("Starting Glassdoor scraping for: {}", glassdoorUrl);
        //             return glassdoorScraperService.scrapeFromGlassdoor(glassdoorUrl);
        //         } catch (Exception e) {
        //             log.error("Glassdoor scraping failed: {}", e.getMessage());
        //             return CompanyDataModel.failed("Glassdoor", e.getMessage());
        //         }
        //     });
        // }

        // Wait for all async operations to complete
        try {
            if (futureClutch != null) {
                CompanyDataModel clutchData = futureClutch.get();
                updateCompanyWithClutchData(companyEntity, clutchData);
            }

            if (futureGoodfirms != null) {
                CompanyDataModel goodfirmsData = futureGoodfirms.get();
                updateCompanyWithGoodfirmsData(companyEntity, goodfirmsData);
            }

            // if (futureGlassdoor != null) {
            //     CompanyDataModel glassdoorData = futureGlassdoor.get();
            //     updateCompanyWithGlassdoorData(companyEntity, glassdoorData);
            // }

            // if (futureWebsite != null) {
            //     CompanyDataModel websiteData = futureWebsite.get();
            //     updateCompanyWithWebsiteData(companyEntity, websiteData);
            // }

            // Update company metadata
            companyEntity.setLastScraped(LocalDateTime.now());
            companyEntity.setDataCompletenessScore(calculateCompletenessScore(companyEntity));

            // Save updated company
            companyRepository.save(companyEntity);

            // Set extracted data as workflow variables
            execution.setVariable("extractedData", "Data extracted for " + companyEntity.getName());
            execution.setVariable("extractionTimestamp", System.currentTimeMillis());
            execution.setVariable("companyId", companyEntity.getId());

            log.info("Data extraction completed for company: {} with completeness score: {}", 
                companyEntity.getName(), companyEntity.getDataCompletenessScore());

        } catch (Exception e) {
            log.error("Error processing scraped data: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to process scraped data", e);
        }
    }

    /**
     * Update company with Clutch data
     */
    private void updateCompanyWithClutchData(CompanyEntity companyEntity, CompanyDataModel clutchData) {
        if (clutchData != null && clutchData.getIsSuccessful() != null && clutchData.getIsSuccessful()) {
            log.info("Updating company {} with comprehensive Clutch data", companyEntity.getName());
            
            companyEntity.setClutchStatus(true);
            
            // Basic information
            if (clutchData.getClutchName() != null) {
                companyEntity.setClutchProviderName(clutchData.getClutchName());
            }
            if (clutchData.getClutchSlug() != null) {
                companyEntity.setClutchSlug(clutchData.getClutchSlug());
            }
            if (clutchData.getClutchCompanyLogoURL() != null) {
                companyEntity.setClutchCompanyLogoURL(clutchData.getClutchCompanyLogoURL());
            }
            if (clutchData.getClutchCompanyOverview() != null) {
                companyEntity.setClutchCompanyOverview(clutchData.getClutchCompanyOverview());
            }
            if (clutchData.getClutchCompanyUrl() != null) {
                companyEntity.setClutchCompanyUrl(clutchData.getClutchCompanyUrl());
            }
            if (clutchData.getClutchProfileUrl() != null) {
                companyEntity.setClutchProfileUrl(clutchData.getClutchProfileUrl());
            }
            if (clutchData.getClutchTotalReviews() != null) {
                companyEntity.setClutchTotalReviews(clutchData.getClutchTotalReviews());
            }
            if (clutchData.getClutchMinimumProjectSize() != null) {
                companyEntity.setClutchMinimumProjectSize(clutchData.getClutchMinimumProjectSize());
            }
            if (clutchData.getClutchHourlyRateRange() != null) {
                companyEntity.setClutchHourlyRateRange(clutchData.getClutchHourlyRateRange());
            }
            if (clutchData.getClutchCity() != null) {
                companyEntity.setClutchCity(clutchData.getClutchCity());
            }
            if (clutchData.getClutchCountry() != null) {
                companyEntity.setClutchCountry(clutchData.getClutchCountry());
            }
            
            // Rating information
            if (clutchData.getClutchRating() != null) {
                companyEntity.setClutchRating(clutchData.getClutchRating());
            }
            if (clutchData.getClutchQuality() != null) {
                companyEntity.setClutchQualityRating(clutchData.getClutchQuality());
            }
            if (clutchData.getClutchSchedule() != null) {
                companyEntity.setClutchScheduleRating(clutchData.getClutchSchedule());
            }
            if (clutchData.getClutchCost() != null) {
                companyEntity.setClutchCostRating(clutchData.getClutchCost());
            }
            if (clutchData.getClutchWillingToRefer() != null) {
                companyEntity.setClutchWillingToRefer(clutchData.getClutchWillingToRefer());
            }
            if (clutchData.getClutchOverallRating() != null) {
                companyEntity.setClutchOverallRating(clutchData.getClutchOverallRating());
            }
            
            // Verification information
            if (clutchData.getClutchVerificationBadge() != null) {
                companyEntity.setClutchVerificationBadge(clutchData.getClutchVerificationBadge());
            }
            if (clutchData.getClutchVerified() != null) {
                companyEntity.setClutchVerified(clutchData.getClutchVerified());
            }
            if (clutchData.getClutchVerificationBadgeText() != null) {
                companyEntity.setClutchVerificationBadgeText(clutchData.getClutchVerificationBadgeText());
            }
            
            // Services and industries (convert lists to JSON strings)
            if (clutchData.getClutchAllServices() != null && !clutchData.getClutchAllServices().isEmpty()) {
                companyEntity.setClutchAllServices(String.join(", ", clutchData.getClutchAllServices()));
            }
            if (clutchData.getClutchAllIndustries() != null && !clutchData.getClutchAllIndustries().isEmpty()) {
                companyEntity.setClutchAllIndustries(String.join(", ", clutchData.getClutchAllIndustries()));
            }
            if (clutchData.getClutchTopMentions() != null && !clutchData.getClutchTopMentions().isEmpty()) {
                companyEntity.setTopMentions(String.join(", ", clutchData.getClutchTopMentions()));
            }
            
            // Update general company info if available
            if (clutchData.getDescription() != null) {
                companyEntity.setClutchCompanyDescription(clutchData.getDescription());
            }
            if (clutchData.getClutchFoundedYear() != null) {
                companyEntity.setClutchFoundedYear(clutchData.getClutchFoundedYear());
            }
            if (clutchData.getClutchCompanySize() != null) {
                companyEntity.setClutchCompanySize(clutchData.getClutchCompanySize());
            }
            
        } else {
            companyEntity.setClutchStatus(false);
            log.warn("Clutch scraping failed for company: {}", companyEntity.getName());
        }
    }

    /**
     * Update company with Goodfirms data
     */
    private void updateCompanyWithGoodfirmsData(CompanyEntity companyEntity, CompanyDataModel goodfirmsData) {
        if (goodfirmsData != null && goodfirmsData.getIsSuccessful() != null && goodfirmsData.getIsSuccessful()) {
            log.info("Updating company {} with Goodfirms data", companyEntity.getName());
            
            companyEntity.setGoodfirmsStatus(true);
            
            // Basic Goodfirms data
            if (goodfirmsData.getGoodfirmsRating() != null) {
                companyEntity.setGoodfirmsRating(goodfirmsData.getGoodfirmsRating());
            }
            if (goodfirmsData.getGoodfirmsReviewsCount() != null) {
                companyEntity.setGoodfirmsReviewsCount(goodfirmsData.getGoodfirmsReviewsCount());
            }
            if (goodfirmsData.getGoodfirmsLocation() != null) {
                companyEntity.setGoodfirmsLocation(goodfirmsData.getGoodfirmsLocation());
            }
            
            // Update general company information from Goodfirms if not already set
            if (companyEntity.getName() == null && goodfirmsData.getName() != null) {
                companyEntity.setName(goodfirmsData.getName());
            }
            
            // Handle client feedback lists (convert to comma-separated strings for storage)
            if (goodfirmsData.getClientLikes() != null && !goodfirmsData.getClientLikes().isEmpty()) {
                String likesString = String.join("; ", goodfirmsData.getClientLikes());
                log.info("Storing {} client likes: {}", goodfirmsData.getClientLikes().size(), 
                        likesString.length() > 100 ? likesString.substring(0, 100) + "..." : likesString);
                companyEntity.setGoodfirmsClientLikes(likesString);
            }
            
            if (goodfirmsData.getClientDislikes() != null && !goodfirmsData.getClientDislikes().isEmpty()) {
                String dislikesString = String.join("; ", goodfirmsData.getClientDislikes());
                log.info("Storing {} client dislikes: {}", goodfirmsData.getClientDislikes().size(), 
                        dislikesString.length() > 100 ? dislikesString.substring(0, 100) + "..." : dislikesString);
                companyEntity.setGoodfirmsClientDislikes(dislikesString);
            }
            
            // Store additional Goodfirms information
            if (goodfirmsData.getGoodfirmProviderName() != null) {
                companyEntity.setGoodfirmsProviderName(goodfirmsData.getGoodfirmProviderName());
            }
            
            if (goodfirmsData.getGoodfirmProfileUrl() != null) {
                companyEntity.setGoodfirmsProfileUrl(goodfirmsData.getGoodfirmProfileUrl());
            }
            
            if (goodfirmsData.getGoodfirmVerificationBadge() != null) {
                companyEntity.setGoodfirmsVerificationBadge(goodfirmsData.getGoodfirmVerificationBadge());
            }
            
            log.info("Successfully updated company {} with Goodfirms data", companyEntity.getName());
            
        } else {
            companyEntity.setGoodfirmsStatus(false);
            log.warn("Goodfirms scraping failed for company: {}", companyEntity.getName());
            if (goodfirmsData != null && goodfirmsData.getErrorMessage() != null) {
                log.warn("Goodfirms error: {}", goodfirmsData.getErrorMessage());
            }
        }
    }

    /**
     * Update company with Glassdoor data
     */
    private void updateCompanyWithGlassdoorData(CompanyEntity companyEntity, CompanyDataModel glassdoorData) {
        if (glassdoorData != null && glassdoorData.getIsSuccessful() != null && glassdoorData.getIsSuccessful()) {
            log.info("Updating company {} with Glassdoor data", companyEntity.getName());
            
            companyEntity.setGlassdoorStatus(true);
            
            // Basic Glassdoor data
            if (glassdoorData.getGlassdoorRating() != null) {
                companyEntity.setGlassdoorRating(glassdoorData.getGlassdoorRating());
            }
            if (glassdoorData.getGlassdoorReviewsCount() != null) {
                companyEntity.setGlassdoorReviewsCount(glassdoorData.getGlassdoorReviewsCount());
            }
            if (glassdoorData.getGlassdoorEmployeeSatisfaction() != null) {
                companyEntity.setGlassdoorEmployeeSatisfaction(glassdoorData.getGlassdoorEmployeeSatisfaction());
            }
            
            // Update general company information from Glassdoor if not already set
            if (companyEntity.getName() == null && glassdoorData.getName() != null) {
                companyEntity.setName(glassdoorData.getName());
            }
            
            // Handle category ratings (convert Map to JSON string for storage)
            if (glassdoorData.getCategoryRatings() != null && !glassdoorData.getCategoryRatings().isEmpty()) {
                try {
                    StringBuilder ratingsJson = new StringBuilder("{");
                    boolean first = true;
                    for (Map.Entry<String, Double> entry : glassdoorData.getCategoryRatings().entrySet()) {
                        if (!first) ratingsJson.append(",");
                        ratingsJson.append("\"").append(entry.getKey().replace("\"", "\\\"")).append("\":")
                                  .append(entry.getValue());
                        first = false;
                    }
                    ratingsJson.append("}");
                    companyEntity.setGlassdoorCategoryRatings(ratingsJson.toString());
                    log.info("Stored {} category ratings for Glassdoor", glassdoorData.getCategoryRatings().size());
                } catch (Exception e) {
                    log.warn("Failed to serialize category ratings: {}", e.getMessage());
                }
            }
            
            // Handle ratings distribution (convert Map to JSON string for storage)
            if (glassdoorData.getRatingsDistribution() != null && !glassdoorData.getRatingsDistribution().isEmpty()) {
                try {
                    StringBuilder distributionJson = new StringBuilder("{");
                    boolean first = true;
                    for (Map.Entry<String, String> entry : glassdoorData.getRatingsDistribution().entrySet()) {
                        if (!first) distributionJson.append(",");
                        distributionJson.append("\"").append(entry.getKey().replace("\"", "\\\"")).append("\":\"")
                                      .append(entry.getValue().replace("\"", "\\\"")).append("\"");
                        first = false;
                    }
                    distributionJson.append("}");
                    companyEntity.setGlassdoorRatingsDistribution(distributionJson.toString());
                    log.info("Stored {} ratings distribution entries for Glassdoor", glassdoorData.getRatingsDistribution().size());
                } catch (Exception e) {
                    log.warn("Failed to serialize ratings distribution: {}", e.getMessage());
                }
            }
            
            // Handle pros (convert List to semicolon-separated string for storage)
            if (glassdoorData.getPros() != null && !glassdoorData.getPros().isEmpty()) {
                String prosString = String.join("; ", glassdoorData.getPros());
                companyEntity.setGlassdoorPros(prosString);
                log.info("Stored {} pros for Glassdoor: {}", glassdoorData.getPros().size(), 
                        prosString.length() > 100 ? prosString.substring(0, 100) + "..." : prosString);
            }
            
            // Handle cons (convert List to semicolon-separated string for storage)
            if (glassdoorData.getCons() != null && !glassdoorData.getCons().isEmpty()) {
                String consString = String.join("; ", glassdoorData.getCons());
                companyEntity.setGlassdoorCons(consString);
                log.info("Stored {} cons for Glassdoor: {}", glassdoorData.getCons().size(), 
                        consString.length() > 100 ? consString.substring(0, 100) + "..." : consString);
            }
            
            log.info("Successfully updated company {} with Glassdoor data", companyEntity.getName());
            
        } else {
            companyEntity.setGlassdoorStatus(false);
            log.warn("Glassdoor scraping failed for company: {}", companyEntity.getName());
            if (glassdoorData != null && glassdoorData.getErrorMessage() != null) {
                log.warn("Glassdoor error: {}", glassdoorData.getErrorMessage());
            }
        }
    }

    /**
     * Update company with Website data
     */
    private void updateCompanyWithWebsiteData(CompanyEntity companyEntity, CompanyDataModel websiteData) {
        if (websiteData != null && websiteData.getIsSuccessful() != null && websiteData.getIsSuccessful()) {
            log.info("Updating company {} with Website data", companyEntity.getName());
            
            companyEntity.setWebsiteStatus(true);
            
            try {
                // Extract WebsiteInfo from the CompanyDataModel
                WebsiteInfo websiteInfo = WebsiteCrawlerServiceImpl.extractWebsiteInfoFromCompanyDataModel(websiteData);
                
                // Map basic website information
                if (websiteInfo.getName() != null) {
                    companyEntity.setWebsiteName(websiteInfo.getName());
                }
                if (websiteInfo.getUrl() != null) {
                    companyEntity.setWebsiteUrl(websiteInfo.getUrl());
                } else if (websiteData.getClutchCompanyUrl() != null) {
                    companyEntity.setWebsiteUrl(websiteData.getClutchCompanyUrl());
                }
                
                // Map website-specific content fields
                if (websiteInfo.getCultureDescription() != null) {
                    companyEntity.setWebsiteCultureDescription(websiteInfo.getCultureDescription());
                    companyEntity.setWebsiteDescription(websiteInfo.getCultureDescription()); // Also set for backward compatibility
                }
                
                if (websiteInfo.getFocusStatement() != null) {
                    companyEntity.setWebsiteFocusStatement(websiteInfo.getFocusStatement());
                    companyEntity.setWebsiteTitle(websiteInfo.getFocusStatement()); // Also set for backward compatibility
                }
                
                if (websiteInfo.getOutcomeSummary() != null) {
                    companyEntity.setWebsiteOutcomeSummary(websiteInfo.getOutcomeSummary());
                }
                
                if (websiteInfo.getFoundedYear() != null) {
                    companyEntity.setWebsiteFoundedYear(websiteInfo.getFoundedYear().toString());
                }
                
                if (websiteInfo.getEmployeeSize() != null) {
                    companyEntity.setWebsiteEmployeeSize(websiteInfo.getEmployeeSize());
                }
                
                if (websiteInfo.getFundingStatus() != null) {
                    companyEntity.setWebsiteFundingStatus(websiteInfo.getFundingStatus());
                }
                
                // Handle location data
                if (websiteInfo.getCountry() != null) {
                    companyEntity.setWebsiteHeadquartersLocation(websiteInfo.getCountry());
                    
                    // Split location into city and country
                    String[] locationParts = websiteInfo.getCountry().split(",");
                    if (locationParts.length >= 2) {
                        companyEntity.setWebsiteCity(locationParts[0].trim());
                        companyEntity.setWebsiteCountry(locationParts[locationParts.length - 1].trim());
                    } else {
                        companyEntity.setWebsiteCity(websiteInfo.getCountry());
                    }
                }
                
                // Handle aliases
                if (websiteInfo.getAliases() != null && !websiteInfo.getAliases().isEmpty()) {
                    companyEntity.setWebsiteAliases(String.join(", ", websiteInfo.getAliases()));
                }
                
                // Map industries to website-specific field
                if (websiteInfo.getIndustries() != null && !websiteInfo.getIndustries().isEmpty()) {
                    List<String> industryNames = websiteInfo.getIndustries().stream()
                            .map(Industry::getName)
                            .toList();
                    companyEntity.setWebsiteIndustries(String.join(", ", industryNames));
                }
                
                // Map product services to website-specific fields
                if (websiteInfo.getProductServices() != null && !websiteInfo.getProductServices().isEmpty()) {
                    List<String> serviceNames = websiteInfo.getProductServices().stream()
                            .map(ProductService::getName)
                            .toList();
                    companyEntity.setWebsiteServices(String.join(", ", serviceNames));
                    
                    // Create detailed services description with features
                    StringBuilder servicesDetail = new StringBuilder();
                    for (ProductService service : websiteInfo.getProductServices()) {
                        servicesDetail.append(service.getName()).append(": ").append(service.getDescription());
                        if (service.getFeatures() != null && !service.getFeatures().isEmpty()) {
                            List<String> featureNames = service.getFeatures().stream()
                                    .map(Feature::getName)
                                    .toList();
                            servicesDetail.append(" (Features: ").append(String.join(", ", featureNames)).append(")");
                        }
                        servicesDetail.append("; ");
                    }
                    companyEntity.setWebsiteServiceDetails(servicesDetail.toString());
                }
                
                // Map achievements to website-specific field
                if (websiteInfo.getAchievements() != null && !websiteInfo.getAchievements().isEmpty()) {
                    List<String> achievementDetails = websiteInfo.getAchievements().stream()
                            .map(achievement -> {
                                StringBuilder sb = new StringBuilder();
                                sb.append(achievement.getName()).append(" (").append(achievement.getType()).append(")");
                                if (achievement.getDescription() != null) {
                                    sb.append(": ").append(achievement.getDescription());
                                }
                                if (achievement.getYear() != null) {
                                    sb.append(" [").append(achievement.getYear()).append("]");
                                }
                                return sb.toString();
                            })
                            .toList();
                    companyEntity.setWebsiteAchievements(String.join("; ", achievementDetails));
                }
                
                // Map other info to website-specific fields
                if (websiteInfo.getOtherInfo() != null) {
                    WebsiteInfo.OtherCompanyInfo otherInfo = websiteInfo.getOtherInfo();
                    
                    if (otherInfo.getFoundingStory() != null) {
                        companyEntity.setWebsiteFoundingStory(otherInfo.getFoundingStory());
                    }
                    
                    if (otherInfo.getGlobalPresence() != null && !otherInfo.getGlobalPresence().isEmpty()) {
                        companyEntity.setWebsiteGlobalPresence(String.join(", ", otherInfo.getGlobalPresence()));
                        // Also set as keywords for backward compatibility
                        companyEntity.setWebsiteKeywords(String.join(", ", otherInfo.getGlobalPresence()));
                    }
                    
                    if (otherInfo.getInitiatives() != null && !otherInfo.getInitiatives().isEmpty()) {
                        companyEntity.setWebsiteInitiatives(String.join(", ", otherInfo.getInitiatives()));
                    }
                }
                
                // Set general name if company doesn't have one yet
                if (companyEntity.getName() == null && websiteInfo.getName() != null) {
                    companyEntity.setName(websiteInfo.getName());
                }
                
                log.info("Successfully updated company {} with comprehensive Website data", companyEntity.getName());
                
            } catch (Exception e) {
                log.error("Error processing website data for company {}: {}", companyEntity.getName(), e.getMessage(), e);
                
                // Fallback to basic data from CompanyDataModel
                if (websiteData.getName() != null && companyEntity.getWebsiteName() == null) {
                    companyEntity.setWebsiteName(websiteData.getName());
                }
                if (websiteData.getWebsiteDescription() != null && companyEntity.getWebsiteDescription() == null) {
                    companyEntity.setWebsiteDescription(websiteData.getWebsiteDescription());
                }
                if (websiteData.getWebsiteTitle() != null && companyEntity.getWebsiteTitle() == null) {
                    companyEntity.setWebsiteTitle(websiteData.getWebsiteTitle());
                }
            }
            
        } else {
            companyEntity.setWebsiteStatus(false);
            log.warn("Website scraping failed for company: {}", companyEntity.getName());
            if (websiteData != null && websiteData.getErrorMessage() != null) {
                log.warn("Website error: {}", websiteData.getErrorMessage());
            }
        }
    }

    /**
     * Calculate data completeness score based on available data
     */
    private Double calculateCompletenessScore(CompanyEntity companyEntity) {
        int totalSources = 5; // Clutch, Goodfirms, Glassdoor, LinkedIn, Website
        int completedSources = 0;

        if (Boolean.TRUE.equals(companyEntity.getClutchStatus())) completedSources++;
        if (Boolean.TRUE.equals(companyEntity.getGoodfirmsStatus())) completedSources++;
        if (Boolean.TRUE.equals(companyEntity.getGlassdoorStatus())) completedSources++;
        if (Boolean.TRUE.equals(companyEntity.getLinkedinStatus())) completedSources++;
        if (Boolean.TRUE.equals(companyEntity.getWebsiteStatus())) completedSources++;

        return (double) completedSources / totalSources * 100.0;
    }
}
