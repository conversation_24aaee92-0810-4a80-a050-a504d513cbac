package com.enosisbd.app.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

// --- Platform ---
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Platform {
    private UUID platformId;
    private String name;
    private String details;

    // For Many-to-Many relationship with ProductService (managed through ProductServicePlatform table)
    // In a real JPA/Hibernate setup, this would often be mapped with @ManyToMany
    private List<ProductService> productServices = new ArrayList<>();
}
