package com.enosisbd.app.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;
import java.util.ArrayList;
import java.util.UUID;

// --- Company ---
@Data // Generates getters, setters, toString, equals, and hashCode
@NoArgsConstructor // Generates a no-argument constructor
@AllArgsConstructor // Generates an all-argument constructor
@Builder // Provides a builder pattern for object creation
public class WebsiteInfo {
    private UUID companyId;
    private String name;
    private List<String> aliases = new ArrayList<>(); // Initialize to avoid null pointer
    private String url;
    private String country;
    private String city;
    private String timeZone;
    private Integer numberOfOffices;
    private Integer foundedYear;
    private String fundingStatus;
    private String employeeSize;
    private String serviceSummary;
    private List<String> allServices;
    private List<String> allIndustries;
    private String cultureDescription;
    private String focusStatement;
    private String outcomeSummary;
    private OtherCompanyInfo otherInfo;

    // Project/Product Fields (86-103)
    @Builder.Default
    private List<Project> projects = new ArrayList<>();
    
    @Builder.Default
    private List<Product> products = new ArrayList<>();

    // Award Fields (104-108)
    @Builder.Default
    private List<Award> awards = new ArrayList<>();

    // For Many-to-Many relationship with Industry (managed through CompanyIndustry table)
    // In a real JPA/Hibernate setup, this would often be mapped with @ManyToMany
    @Builder.Default
    private List<Industry> industries = new ArrayList<>();

    // For One-to-Many relationship with ProductService
    // In a real JPA/Hibernate setup, this would often be mapped with @OneToMany
    @Builder.Default
    private List<ProductService> productServices = new ArrayList<>();

    // For One-to-Many relationship with Achievement
    // In a real JPA/Hibernate setup, this would often be mapped with @OneToMany
    @Builder.Default
    private List<Achievement> achievements = new ArrayList<>();

    // Nested Models --------------------------------------------------

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TeamMember {
        private String title;
        private String teamMemberName;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Client {
        private String client;
        private String clientName;
        private String clientSiteURL;
        private String clientLogo;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Project {
        private String name;                // Field 86
        private String url;                 // Field 87
        private String clientName;          // Field 88
        private String industry;            // Field 89
        private String platform;            // Field 90
        @Builder.Default
        private List<String> techUsed = new ArrayList<>();      // Field 91
        @Builder.Default
        private List<String> keyFeatures = new ArrayList<>();   // Field 92
        private String focus;               // Field 93
        private String outcome;             // Field 94
        private String otherInfo;           // Field 95
        private String thumbnailUrl;        // Field 96
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Product {
        private String name;                // Field 97
        private String logoUrl;             // Field 98
        private String type;                // Field 99
        @Builder.Default
        private List<String> platforms = new ArrayList<>();     // Field 100
        @Builder.Default
        private List<String> techUsed = new ArrayList<>();      // Field 101
        @Builder.Default
        private List<String> keyFeatures = new ArrayList<>();   // Field 102
        private String otherInfo;           // Field 103
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Award {
        private String name;                // Field 104
        private String url;                 // Field 105
        private String awardedBy;           // Field 106
        private Integer year;               // Field 107
        private String imageUrl;            // Field 108
        private Boolean isVerified;         // Additional verification flag
    }

    // Nested object for Company's less structured info
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class OtherCompanyInfo {
        @Builder.Default
        private List<String> globalPresence = new ArrayList<>();
        private String foundingStory;
        @Builder.Default
        private List<String> initiatives = new ArrayList<>();
    }
}

