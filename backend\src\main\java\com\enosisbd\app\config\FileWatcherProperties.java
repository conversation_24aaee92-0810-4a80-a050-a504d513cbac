package com.enosisbd.app.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "file-watcher")
public class FileWatcherProperties {
    
    private String inputFolder = "./input";
    private String outputFolder = "./output";
    private String processedFolder = "./processed";
    private String errorFolder = "./error";
    private int pollingIntervalSeconds = 5;
    private boolean enabled = true;
    private List<String> supportedExtensions = List.of("json");
    private boolean autoCreateFolders = true;
    
    // Processing configuration
    private int processingTimeoutMinutes = 1;
    private int largeFileThresholdMb = 5;
    private boolean disableAutoCommitForLargeFiles = true;
    private int maxConcurrentFiles = 3;
} 