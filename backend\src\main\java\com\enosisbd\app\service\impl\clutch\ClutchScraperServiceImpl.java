package com.enosisbd.app.service.impl.clutch;

import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.TreeMap;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import com.enosisbd.app.model.CompanyDataModel;
import com.enosisbd.app.service.ClutchScraperService;
import com.enosisbd.app.service.crawler.GenericCrawlerService;
import com.enosisbd.app.service.crawler.HeaderGenerator;
import com.enosisbd.app.service.crawler.UserAgentRotator;
import com.enosisbd.app.util.ClutchUrlExtractor;
import com.microsoft.playwright.Browser;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.BrowserType;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.ViewportSize;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ClutchScraperServiceImpl implements ClutchScraperService {

    private final GenericCrawlerService genericCrawlerService;
    private final UserAgentRotator userAgentRotator;
    private final HeaderGenerator headerGenerator;
    private final Random random = new Random();

    @Override
    @Retryable(
        maxAttempts = 3,
        backoff = @Backoff(
            delay = 1000,
            multiplier = 2.0,
            maxDelay = 10000
        )
    )
    
    public CompanyDataModel scrapeFromClutch(String profileUrl) {
        long startTime = System.currentTimeMillis();
        log.info("Starting to scrape Clutch data for company: {} and populate Company object", profileUrl);

        CompanyDataModel companyDataModel = CompanyDataModel.success("clutch");

        try {
            // Define the selectors for text content
            Map<String, String> textSelectors = new HashMap<>();
            textSelectors.put("companyName", "#header > div > div.profile-header > div.profile-header__badges > h1");
            textSelectors.put("reviewNumber", "#header > div > div.profile-header > div.profile-header__badges > div > button.profile-rating.sg-rating__reviews.scroll-to-reviews");
            textSelectors.put("companyInfo", "#summary_section");
            textSelectors.put("websiteLink", "#header > div > div.profile-header > div.profile-header__short-actions > ul > li.profile-short-actions__item.profile-short-actions__item--visit-website > a");

            // Company details - titles
            textSelectors.put("minProjectSize", "#summary_section > ul > li:nth-child(1) > div > span.profile-summary__detail-title");
            textSelectors.put("hourlyRate", "#summary_section > ul > li:nth-child(2) > div > span.profile-summary__detail-title");
            textSelectors.put("employees", "#summary_section > ul > li:nth-child(3) > div > span.profile-summary__detail-title");
            textSelectors.put("locations", "#summary_section > ul > li:nth-child(4) > div > span.profile-summary__detail-title");
            textSelectors.put("foundedYear", "#summary_section > ul > li:nth-child(5) > div > span.profile-summary__detail-title");

            // Add these selectors to your textSelectors map
            textSelectors.put("overallRating", "#reviews-sg-accordion > div > section > div.profile-insights > div.profile-insights__rating.sg-rating.sg-show-more-less__container.sg-show-more-less__container--collapsed > span.sg-rating__number");
            textSelectors.put("qualityRating", "#reviews-sg-accordion > div > section > div.profile-insights > div.profile-insights__rating.sg-rating.sg-show-more-less__container.sg-show-more-less__container--collapsed > ul > li:nth-child(1) > span.profile-insights__rating-list-item-value");
            textSelectors.put("scheduleRating", "#reviews-sg-accordion > div > section > div.profile-insights > div.profile-insights__rating.sg-rating.sg-show-more-less__container.sg-show-more-less__container--collapsed > ul > li:nth-child(2) > span.profile-insights__rating-list-item-value");
            textSelectors.put("costRating", "#reviews-sg-accordion > div > section > div.profile-insights > div.profile-insights__rating.sg-rating.sg-show-more-less__container.sg-show-more-less__container--collapsed > ul > li:nth-child(3) > span.profile-insights__rating-list-item-value");
            textSelectors.put("willingToReferRating", "#reviews-sg-accordion > div > section > div.profile-insights > div.profile-insights__rating.sg-rating.sg-show-more-less__container.sg-show-more-less__container--collapsed > ul > li:nth-child(4) > span.profile-insights__rating-list-item-value");

            // Add this selector to your textSelectors map
            textSelectors.put("topMentionsList", "#reviews-sg-accordion > div > section > div.profile-insights > div.profile-insights__mentions > ul");

            // Add this selector to your textSelectors map to check if the verification badge exists
            textSelectors.put("verificationBadge", "#header > div > div.profile-header > div.profile-header__badges > div.profile-summary__verified-mark");

            // Add this selector to your textSelectors map to check if the verification badge exists
// Add this selector to your textSelectors map to get the verification badge text
            textSelectors.put("verificationBadgeText", "#header > div > div.profile-header > div.profile-header__badges > div.profile-summary__verified-mark.profile-summary__verified-mark--basic.sg-tooltip-v2 > span");


            // Define options for text scraping
            Map<String, Object> textOptions = new HashMap<>();
            textOptions.put("headless", true);
            textOptions.put("waitForSelector", "#summary_section");

            Map<String, Object> textoptionsWithHook = createScrapingOptions(textOptions);

            // Scrape the text data
            Map<String, String> scrapedData = genericCrawlerService.scrapeWithSelectors(profileUrl, textSelectors, textoptionsWithHook);

            // Define options for website link scraping (to extract href attribute)
            Map<String, Object> linkOptions = new HashMap<>();
            linkOptions.put("headless", true);
            linkOptions.put("extractAttributes", true);
            linkOptions.put("attributeName", "href");

            Map<String, Object> linkoptionsWithHook = createScrapingOptions(linkOptions);

            // Scrape the website link URL
            Map<String, String> linkData = genericCrawlerService.scrapeWithSelectors(profileUrl,
                    Map.of("websiteLink", "#header > div > div.profile-header > div.profile-header__short-actions > ul > li.profile-short-actions__item.profile-short-actions__item--visit-website > a"),
                    linkoptionsWithHook);

            // Define selectors for the logo image
            Map<String, String> imageSelectors = new HashMap<>();
            imageSelectors.put("companyLogo", "#header > div > div.profile-header > a > img");

            // Define options for image scraping
            Map<String, Object> imageOptions = new HashMap<>();
            imageOptions.put("headless", true);
            imageOptions.put("extractAttributes", true);
            imageOptions.put("attributeName", "src");

            Map<String, Object> imageoptionsWithHook = createScrapingOptions(imageOptions);

            // Scrape the logo image URL
            Map<String, String> imageData = genericCrawlerService.scrapeWithSelectors(profileUrl, imageSelectors, imageoptionsWithHook);

            // Populate the Company object with scraped data

            // Basic Information
            if (companyDataModel.getClutchName() == null && scrapedData.containsKey("companyName")) {
                companyDataModel.setClutchName(scrapedData.get("companyName"));
            }

            // Need to
            if (companyDataModel.getClutchSlug() == null) {
                // company.setSlug(companySlug);
            }

            if (companyDataModel.getClutchCompanyLogoURL() == null && imageData.containsKey("companyLogo")) {
                companyDataModel.setClutchCompanyLogoURL(imageData.get("companyLogo"));
            }

            if (companyDataModel.getClutchCompanyOverview() == null && scrapedData.containsKey("companyInfo")) {
                companyDataModel.setClutchCompanyOverview(scrapedData.get("companyInfo"));
            }

            // Extract and set the company website URL
            if (companyDataModel.getClutchCompanyUrl() == null && linkData.containsKey("websiteLink")) {
                String clutchRedirectUrl = linkData.get("websiteLink");
                if (clutchRedirectUrl != null && !clutchRedirectUrl.isEmpty()) {
                    // Extract the provider website from the Clutch redirect URL
                    String providerWebsite = ClutchUrlExtractor.extractProviderWebsite(clutchRedirectUrl);
                    if (providerWebsite != null && !providerWebsite.isEmpty()) {
                        // Normalize the URL (ensure it has https:// prefix)
                        String normalizedUrl = ClutchUrlExtractor.normalizeWebsiteUrl(providerWebsite);
                        companyDataModel.setClutchCompanyUrl(normalizedUrl);
                        log.info("Extracted company website URL: {}", normalizedUrl);
                    }
                }
            }

            // Parse and set total reviews
            if (companyDataModel.getClutchTotalReviews() == null && scrapedData.containsKey("reviewNumber")) {
                String reviewText = scrapedData.get("reviewNumber");
                if (reviewText != null && !reviewText.isEmpty()) {
                    try {
                        // Extract just the number from the review text
                        String numericPart = reviewText.replaceAll("[^0-9]", "");
                        if (!numericPart.isEmpty()) {
                            companyDataModel.setClutchTotalReviews(Integer.parseInt(numericPart));
                            companyDataModel.setClutchReviewsCount(Integer.parseInt(numericPart));
                        }
                    } catch (NumberFormatException e) {
                        log.warn("Could not parse review number: {}", reviewText);
                    }
                }
            }

            // Set profile URL
            if (companyDataModel.getClutchProfileUrl() == null) {
                companyDataModel.setClutchProfileUrl(profileUrl);
            }

            // Parse and set minimum project size
            if (companyDataModel.getClutchMinimumProjectSize() == null && scrapedData.containsKey("minProjectSize")) {
                String minProjectSize = scrapedData.get("minProjectSize");
                if (minProjectSize != null && !minProjectSize.isEmpty()) {
                    try {
                        // Extract just the number from the text (e.g., "$1,000+" -> 1000)
                        String numericPart = minProjectSize.replaceAll("[^0-9]", "");
                        if (!numericPart.isEmpty()) {
                            companyDataModel.setClutchMinimumProjectSize(Integer.parseInt(numericPart));
                        }
                    } catch (NumberFormatException e) {
                        log.warn("Could not parse minimum project size: {}", minProjectSize);
                    }
                }
            }

            // Set hourly rate range
            if (companyDataModel.getClutchHourlyRateRange() == null && scrapedData.containsKey("hourlyRate")) {
                companyDataModel.setClutchHourlyRateRange(scrapedData.get("hourlyRate"));
            }

            // Parse and set employee count
            if (companyDataModel.getClutchCompanySize() == null && scrapedData.containsKey("employees")) {
                String employeesText = scrapedData.get("employees");
                if (employeesText != null && !employeesText.isEmpty()) {
                    try {
                        companyDataModel.setClutchCompanySize(employeesText);
                    } catch (NumberFormatException e) {
                        log.warn("Could not parse employee count: {}", employeesText);
                    }
                }
            }

            // Parse location information
            if (companyDataModel.getClutchCity() == null && scrapedData.containsKey("locations")) {
                String locationText = scrapedData.get("locations");
                if (locationText != null && !locationText.isEmpty()) {
                    // Try to parse "City, Country" format
                    String[] parts = locationText.split(",");
                    if (parts.length >= 1) {
                        companyDataModel.setClutchCity(parts[0].trim());
                    }
                    if (parts.length >= 2) {
                        companyDataModel.setClutchCountry(parts[1].trim());
                    }
                }
            }

            // Set founded year
            if (companyDataModel.getClutchFoundedYear() == null && scrapedData.containsKey("foundedYear")) {
                String foundedYear = scrapedData.get("foundedYear");
                if (foundedYear != null && !foundedYear.isEmpty()) {
                    companyDataModel.setClutchFoundedYear(foundedYear.replace("Founded ",""));
                }
            }

            // Set provider name (for consistency with other scrapers)
            if (companyDataModel.getClutchProviderName() == null && scrapedData.containsKey("companyName")) {
                companyDataModel.setClutchProviderName(scrapedData.get("companyName"));
            }

            // Set rating values in the Company object
            if (companyDataModel.getClutchRating() == null && scrapedData.containsKey("overallRating")) {
                companyDataModel.setClutchRating(Double.parseDouble(scrapedData.get("overallRating")));
            }
            if (companyDataModel.getClutchQuality() == null && scrapedData.containsKey("qualityRating")) {
                companyDataModel.setClutchQuality(scrapedData.get("qualityRating"));
            }
            if (companyDataModel.getClutchSchedule() == null && scrapedData.containsKey("scheduleRating")) {
                companyDataModel.setClutchSchedule(scrapedData.get("scheduleRating"));
            }
            if (companyDataModel.getClutchCost() == null && scrapedData.containsKey("costRating")) {
                companyDataModel.setClutchCost(scrapedData.get("costRating"));
            }
            if (companyDataModel.getClutchWillingToRefer() == null && scrapedData.containsKey("willingToReferRating")) {
                try {
                    double rating = Double.parseDouble(scrapedData.get("willingToReferRating"));
                    companyDataModel.setClutchWillingToRefer(rating);
                } catch (Exception e) {
                    log.warn("Could not parse willing to refer rating");
                }
            }
            if (companyDataModel.getClutchOverallRating() == null && scrapedData.containsKey("overallRating")) {
                companyDataModel.setClutchOverallRating(scrapedData.get("overallRating"));
            }

            // Then, in your code where you process the scraped data:
            if (scrapedData.containsKey("topMentionsList")) {
                String topMentionsHtml = scrapedData.get("topMentionsList");
                List<String> topMentions = extractTopMentionsWithCounts(topMentionsHtml);
                companyDataModel.setClutchTopMentions(topMentions);

                // Log the extracted mentions for debugging
                log.info("Extracted top mentions: {}", topMentions);
            }

            // Set verification status
            if (scrapedData.containsKey("verificationBadge")) {
                // If the selector found something, the company is verified
                boolean isVerified = !scrapedData.get("verificationBadge").isEmpty();
                companyDataModel.setClutchVerificationBadge(isVerified);
                companyDataModel.setClutchVerified(isVerified);

                // Log the verification status
                log.info("Company verification status: {}", isVerified);

                // Extract and set verification badge information
                if (scrapedData.containsKey("verificationBadgeText")) {
                    String verificationText = scrapedData.get("verificationBadgeText");
                    log.info("Verification badge text: {}", verificationText);
                    companyDataModel.setClutchVerificationBadgeText(verificationText);
                }
            }

            try {
                final Map<String, String> xPaths = new HashMap<>();
                xPaths.put("services", "/html/body/main/div[2]/section[1]/section[2]/div/div[3]/div[2]/ul");
                Map<String, String> scraped = genericCrawlerService.scrapeWithXPath(profileUrl, xPaths, textoptionsWithHook);

                List<String> services = extractIndustriesWithRegex(scraped.get("services"));
                companyDataModel.setClutchAllServices(services);
            } catch (Exception dx) {
                log.error("Error scraping industries: {}", dx.getMessage());
            }

            try {
                List<String> industries = getIndustries(profileUrl);
                companyDataModel.setClutchAllIndustries(industries);
            } catch (Exception ex) {
                log.error(ex.getMessage());
            }

            long duration = System.currentTimeMillis() - startTime;
            log.info("Successfully scraped Clutch data and populated Company object for {} in {} ms", profileUrl, duration);

        } catch (Exception e) {
            log.error("Error scraping Clutch data for company {}: {}", profileUrl, e.getMessage());
            throw e; // Let the retry mechanism handle it
        }

        return companyDataModel;
    }

    @Recover
    public CompanyDataModel fallback(Exception e, String url) {
        log.error("All retry attempts failed for Clutch scraping: {}", url, e);
        return CompanyDataModel.failed("clutch", 
            String.format("Failed after all retry attempts. Last error: %s", e.getMessage()));
    }

    @SafeVarargs
    private Map<String, Object> createScrapingOptions(Map<String, Object>... customOptions) {
        Map<String, Object> options = new HashMap<>();

        // Add a pre-scraping hook for human-like behavior.
        options.put("preScrapingHook", (Consumer<Page>) page -> {
            try {
                log.debug("Executing pre-scraping hook for human-like behavior on Clutch.");
                page.waitForTimeout(2000 + random.nextInt(3500));

                // On Clutch, sometimes a modal pops up. We can try to close it if it exists.
                Locator modal = page.locator(".modal-backdrop");
                if (modal.isVisible()) {
                    log.debug("Closing modal popup.");
                    page.locator("button.close").click();
                    page.waitForTimeout(1000 + random.nextInt(1000));
                }

                log.debug("Simulating scrolling on the page to trigger lazy-loaded content.");
                for (int i = 0; i < 3; i++) {
                    page.evaluate("window.scrollBy(0, " + (500 + random.nextInt(400)) + ")");
                    page.waitForTimeout(800 + random.nextInt(800));
                }

            } catch (Exception e) {
                log.error("Error during pre-scraping hook: {}", e.getMessage(), e);
            }
        });

        // Merge custom options, allowing them to override the defaults.
        if (customOptions != null) {
            for (Map<String, Object> customMap : customOptions) {
                if (customMap != null) {
                    options.putAll(customMap);
                }
            }
        }

        return options;
    }

    private List<String> getIndustries(String profileURL) {
        List<String> industries = new ArrayList<>();
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(new BrowserType.LaunchOptions()
                    .setHeadless(true));
            int[] viewport = headerGenerator.generateRandomViewport(false);

            Browser.NewContextOptions contextOptions = new Browser.NewContextOptions()
                    .setUserAgent(userAgentRotator.getRandomDesktopUserAgent())
                    .setViewportSize(new ViewportSize(viewport[0], viewport[1]));
            BrowserContext context = browser.newContext(contextOptions);
            Page page = context.newPage();
            headerGenerator.applyHeadersToPage(page, false, null);

            // Navigation with retries
            for (int attempt = 0; attempt < 3; attempt++) {
                try {
                    page.navigate(profileURL);
                    page.waitForLoadState(LoadState.DOMCONTENTLOADED);
                    break;
                } catch (Exception e) {
                    if (attempt == 2) throw e;
                    page.waitForTimeout(2000);
                }
            }
            log.info("Navigated to profile URL: {}", profileURL);

            try {
                // Wait for the chartPie object to be available
                page.waitForFunction("() => typeof window.chartPie !== 'undefined'");

                // Extract industries data directly from JavaScript
                Object industriesData = page.evaluate(
                        "() => {" +
                                "  if (window.chartPie && window.chartPie.industries && window.chartPie.industries.slices) {" +
                                "    return window.chartPie.industries.slices.map((slice, index) => ({" +
                                "      index: index + 1," +
                                "      name: slice.name," +
                                "      percent: slice.percent" +
                                "    }));" +
                                "  }" +
                                "  return [];" +
                                "}"
                );

                // Convert the result to List<String>
                if (industriesData instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> industriesList = (List<Map<String, Object>>) industriesData;

                    for (Map<String, Object> industry : industriesList) {
                        String name = (String) industry.get("name");
                        Integer index = (Integer) industry.get("index");

                        if (name != null && !name.isEmpty()) {
                            // Decode HTML entities if needed
                            String decodedName = name.replace("\\u0026", "&")
                                    .replace("\\u003c", "<")
                                    .replace("\\u003e", ">");
                            industries.add(index + ". " + decodedName);
                        }
                    }
                }

                log.info("Extracted {} industries: {}", industries.size(), industries);

            } catch (Exception e) {
                log.error("Error extracting industries", e);
                page.screenshot(new Page.ScreenshotOptions()
                        .setPath(Paths.get("error-screenshot.png")));
            } finally {
                browser.close();
            }
        } catch (Exception e) {
            log.error("Playwright error", e);
        }
        return industries;
    }

    private List<String> extractIndustriesWithRegex(String text) {
        List<String> industries = new ArrayList<>();
        if (text == null || text.isEmpty()) {
            return industries;
        }

        // Clean up the text - remove HTML tags
        text = text.replaceAll("<[^>]*>", " ");

        // Pattern to match industry name followed by percentage
        // This looks for words followed by a number with % sign
        Pattern pattern = Pattern.compile("([\\w\\s&-]+)\\s+(\\d+%)");
        Matcher matcher = pattern.matcher(text);

        int index = 1;
        while (matcher.find()) {
            String industryName = matcher.group(1).trim();

            if (!industryName.isEmpty()) {
                industries.add(index + ". " + industryName);
                index++;
            }
        }

        return industries;
    }

    private List<String> extractTopMentionsWithCounts(String html) {
        List<String> mentions = new ArrayList<>();
        if (html == null || html.isEmpty()) {
            return mentions;
        }

        // Regex to extract mention text and count (e.g., "High-quality work (3)")
        Pattern pattern = Pattern.compile("([\\w\\s-']+?)\\s*\\((\\d+)\\)");
        Matcher matcher = pattern.matcher(html);

        // Use a map to store mentions by count for sorting
        Map<Integer, List<String>> mentionsByCount = new TreeMap<>(Collections.reverseOrder());

        while (matcher.find()) {
            String mentionText = matcher.group(1).trim();
            int count = Integer.parseInt(matcher.group(2).trim());

            if (!mentionText.isEmpty()) {
                // Group mentions by their count
                mentionsByCount.computeIfAbsent(count, k -> new ArrayList<>())
                    .add(mentionText);
            }
        }

        // Build the final list in descending order of count
        for (Map.Entry<Integer, List<String>> entry : mentionsByCount.entrySet()) {
            int count = entry.getKey();
            List<String> mentionsForCount = entry.getValue();
            
            // Sort mentions alphabetically within the same count
            Collections.sort(mentionsForCount);
            
            for (String mention : mentionsForCount) {
                mentions.add(mention + " (" + count + ")");
            }
        }

        return mentions;
    }

}
