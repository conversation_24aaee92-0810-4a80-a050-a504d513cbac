package com.enosisbd.app.controller;

import com.enosisbd.app.entity.ScrapeEntity;
import com.enosisbd.app.controller.request.ScrapeRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface ScraperApi {
    ResponseEntity<?> scrape(ScrapeRequest scrapeRequest);
    
    /**
     * Convert the enosis.json file to CSV format
     * @return ResponseEntity containing the CSV file as bytes
     */
    ResponseEntity<byte[]> convertJsonToCsv();
    
    /**
     * Upload a CSV file containing multiple scrape requests
     * @param file The CSV file containing company data
     * @return ResponseEntity containing upload status and any errors
     */
    ResponseEntity<?> uploadCsv(MultipartFile file);
    
    /**
     * Get all scrape entities with QUEUE status
     * @return ResponseEntity containing list of queued scrape entities
     */
    ResponseEntity<List<ScrapeEntity>> getAllQueuedScrapes();
    
    /**
     * Get all scrape entities with SCHEDULED status
     * @return ResponseEntity containing list of scheduled scrape entities
     */
    ResponseEntity<List<ScrapeEntity>> getAllScheduledScrapes();
    
    /**
     * Get all scrape entities currently IN_PROGRESS
     * @return ResponseEntity containing list of processing scrape entities
     */
    ResponseEntity<List<ScrapeEntity>> getAllProcessingScrapes();
    
    /**
     * Get all scrape entities with SUCCESS status
     * @return ResponseEntity containing list of completed scrape entities
     */
    ResponseEntity<List<ScrapeEntity>> getAllCompletedScrapes();
    
    /**
     * Get all scrape entities with FAILED status
     * @return ResponseEntity containing list of failed scrape entities
     */
    ResponseEntity<List<ScrapeEntity>> getAllFailedScrapes();
}
