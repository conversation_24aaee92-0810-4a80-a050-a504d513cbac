package com.enosisbd.app.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

// --- CompanyIndustry (Junction Table) ---
// This class is primarily for database mapping when defining Many-to-Many relationships
// explicitly via a join table. In a Java model, you often manage this implicitly
// through @ManyToMany mappings in Company and Industry classes.
// However, if you need a specific entity for the join table (e.g., if it has extra attributes),
// you would define it like this.
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CompanyIndustry {
    private UUID companyId; // Foreign Key to Company
    private UUID industryId; // Foreign Key to Industry
}
