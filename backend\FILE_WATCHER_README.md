# File Watcher System for Scrape Requests

This system monitors an input folder for JSON files containing lists of ScrapeRequest objects and automatically processes them through the scraping pipeline.

## How It Works

1. **Input Monitoring**: The system continuously monitors the configured input folder for new JSON files
2. **JSON Processing**: When a JSON file is detected, it's parsed and converted into scrape requests
3. **Scrape Execution**: Each scrape request is queued and processed through the existing scraping system
4. **Output Generation**: Successfully scraped company data is automatically exported to the output folder
5. **File Organization**: Processed files are moved to appropriate folders based on their status

## Folder Structure

```
./input/          # Place JSON files here for processing
./output/         # Scraped company data appears here
./processed/      # Successfully processed input files
./error/          # Failed input files with error details
```

## Configuration

The system is configured through application.yaml with these settings:

### Basic Configuration
- `input-folder`: Where to watch for JSON files (default: ./input)
- `output-folder`: Where to save scraped data (default: ./output)
- `processed-folder`: Where to move processed files (default: ./processed)
- `error-folder`: Where to move failed files (default: ./error)
- `polling-interval-seconds`: How often to check for new files (default: 5)
- `enabled`: Enable/disable the file watcher (default: true)
- `supported-extensions`: File types to process (default: [json])
- `auto-create-folders`: Auto-create folders if they don't exist (default: true)

### Advanced Processing Configuration
- `processing-timeout-minutes`: Timeout for processing each file (default: 1)
- `large-file-threshold-mb`: File size threshold to be considered "large" (default: 5MB)
- `disable-auto-commit-for-large-files`: Disable database auto-commit for large files (default: true)
- `max-concurrent-files`: Maximum number of files to process simultaneously (default: 3)

## JSON Input Formats

### Format 1: Simple Array of ScrapeRequests
```json
[
  {
    "name": "Company Name",
    "glassdoor": "https://www.glassdoor.com/Reviews/Company-Reviews-E123456.htm",
    "clutch": "https://clutch.co/profile/company-name",
    "goodfirms": "https://www.goodfirms.co/company/company-name",
    "linkedin": "https://www.linkedin.com/company/company-name",
    "website": "https://www.company.com"
  }
]
```

### Format 2: ScrapeRequestBatch with Metadata
```json
{
  "batchId": "custom_batch_001",
  "description": "Q4 2024 analysis batch",
  "createdAt": "2024-01-15T10:30:00",
  "totalRequests": 1,
  "scrapeRequests": [
    {
      "name": "Company Name",
      "glassdoor": "https://www.glassdoor.com/Reviews/Company-Reviews-E123456.htm",
      "clutch": "https://clutch.co/profile/company-name",
      "goodfirms": "https://www.goodfirms.co/company/company-name", 
      "linkedin": "https://www.linkedin.com/company/company-name",
      "website": "https://www.company.com"
    }
  ]
}
```

## Usage Instructions

1. **Prepare Your JSON File**: Create a JSON file with your scrape requests
2. **Place in Input Folder**: Copy your JSON file to the ./input/ folder
3. **Monitor Processing**: Watch the logs to see the file being processed
4. **Check Results**: 
   - Batch summary appears in ./output/summary_[batchId].json
   - Individual company data appears in ./output/company_[name]_[id]_[timestamp].json
   - Original file moves to ./processed/ folder

## Example Files

See the included example files:
- example_scrape_requests.json - Simple array format
- example_scrape_batch.json - Full batch format with metadata

## Error Handling

If a file fails to process:
1. The original file is moved to the ./error/ folder
2. An error details file ([filename].error) is created with the failure reason
3. The error is logged for debugging

## Advanced Features

### Large File Handling
- Files larger than the configured threshold (5MB by default) are processed with special handling
- Auto-commit is disabled for large files to improve performance
- Longer timeout periods are applied for large file processing
- Manual commit is performed after successful processing

### Timeout Protection
- Each file processing operation has a configurable timeout (1 minute by default)  
- Files that exceed the timeout are moved to the error folder
- Prevents system hang-ups from problematic files

### Concurrent Processing Control
- Maximum number of concurrent file processing operations is configurable
- Uses semaphore-based limiting to prevent resource exhaustion
- Graceful handling of processing queues

### Real-time Result Saving
- Scraped company data is saved immediately upon completion
- No need to wait for all scraping to finish before seeing results
- Progress tracking shows completion status in real-time

### Comprehensive Error Handling
- Processing errors are tracked and included in batch summaries
- Failed requests are logged with detailed error messages
- Fallback export mechanism for scrapes not processed through file watcher

## Integration

The file watcher integrates seamlessly with the existing scraping infrastructure using the same ScrapeRequest model and ScraperService for processing. 