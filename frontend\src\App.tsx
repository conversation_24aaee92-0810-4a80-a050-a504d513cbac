import React from 'react'
import { <PERSON>rows<PERSON><PERSON>outer, Routes, Route } from 'react-router-dom'
import { RootLayout } from './components/layout/root-layout'
import { DashboardOverview } from './components/dashboard/dashboard-overview'
import { ScrapingJobs } from './components/dashboard/scraping-jobs'
import { Toaster } from 'sonner'
import './App.css'

function App() {
  return (
    <BrowserRouter>
      <RootLayout>
        <Routes>
          <Route path="/" element={<DashboardOverview />} />
          <Route path="/scraping" element={<ScrapingJobs />} />
        </Routes>
      </RootLayout>
      <Toaster />
    </BrowserRouter>
  )
}

export default App