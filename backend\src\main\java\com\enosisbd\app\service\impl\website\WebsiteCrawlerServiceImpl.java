package com.enosisbd.app.service.impl.website;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import com.enosisbd.app.gemini.GeminiClient;
import com.enosisbd.app.gemini.GeminiRequest;
import com.enosisbd.app.gemini.GeminiResponse;
import com.enosisbd.app.model.Achievement;
import com.enosisbd.app.model.CompanyDataModel;
import com.enosisbd.app.model.Feature;
import com.enosisbd.app.model.Industry;
import com.enosisbd.app.model.ProductService;
import com.enosisbd.app.model.WebsiteInfo;
import com.enosisbd.app.service.WebsiteScraperService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class WebsiteCrawlerServiceImpl implements WebsiteScraperService {

    private static final String SYSTEM_PROMPT = "You are a professional web scraper. Your task is to analyze the provided website URL and extract specific information about the company. Follow these instructions carefully:\n" +
            "Task 1: Awards and Certifications (Default if not specified)\n" +
            "Objective: To compile an exhaustive list of awards and certifications for specific companies, using only information available on their official websites.\n" +
            "Methodology:\n" +
            "Comprehensive Website Exploration: Examine main sections ('About Us', 'Company Overview', 'Our Team'), dedicated sections ('Awards', 'Accolades', 'Certifications', 'Achievements', 'Recognitions'), supplementary pages ('News', 'Press Releases', 'Blog', 'Media', 'Events'), footer links, and sitemap.\n" +
            "On-Site Search Functionality: If available, use keywords such as 'award', 'certification', 'recognition', 'accolade', and 'achievement'.\n" +
            "Visual and Textual Content Review: Inspect images, infographics, badges, and read textual content for references to recognitions (including in employee testimonials, case studies, or client feedback).\n" +
            "Verification of External Links: Follow any external links provided on the website that lead to third-party validations or listings of awards and certifications to confirm their authenticity. However, the source URL must always be from the company's official website.\n" +
            "Documentation and Cross-Verification: Record all findings meticulously, noting the exact page and context. Cross-verify information across different sections of the website to ensure consistency and accuracy.\n" +
            "Important Notes:\n" +
            "Only use information available on the company's official website. Do not include information from external sources or third-party websites.\n" +
            "Ensure the accuracy of the information by double-checking and cross-referencing within the website.\n" +
            "If a specific detail (e.g., year) is not available, leave the field empty or null.\n" +
            "Data should be ordered chronologically with the latest year first.\n" +
            "Provide at least 10 awards and 10 certifications, if applicable.\n" +
            "The output will be in JSON format, as specified later.\n" +
            "Task 2: Confirmed Client Companies\n" +
            "Objective: Identify confirmed client companies from a software service outsourcing company's official website.\n" +
            "Instructions:\n" +
            "Website Exploration: Thoroughly examine the entire official company website. Identify and list companies that are explicitly labeled as clients. Aim to find a minimum of 15 confirmed client names, if available.\n" +
            "Client Verification: For each confirmed client, locate their official LinkedIn company profile URL. Verify the LinkedIn profile by matching: Logo, Industry, and Website domain (if available).\n" +
            "Exclusions:\n" +
            "Do not include Partners, Integration platforms, Hiring partners.\n" +
            "Exclude Logo carousels unless they are clearly marked as clients.\n" +
            "Exclude or mark as unverifiable any: Ambiguous names (e.g., \"Matrix\", \"CEI\"), or companies for which a confident LinkedIn match cannot be made. Avoid matching based solely on name similarity.\n" +
            "Output Format: The output will be in JSON format, as specified later.\n" +
            "Task 3: Projects and Products\n" +
            "Objective: Explore the official parent company website URL (not individual project URLs), extract publicly available information on at least 5 projects (if available) and 5 products (if available).\n" +
            "Instructions:\n" +
            "All URLs must be strictly from the parent company's official website. Do not use external sources or search engines.\n" +
            "For Projects: Include Industry, but leave Product Type empty or null.\n" +
            "For Products: Include Product Type, but leave Industry empty or null.\n" +
            "Company Name must be repeated in each entry.\n" +
            "All entries should be clean, structured, and professionally toned, without templates or repetition.\n" +
            "Product Type List for matching (do not create new types, use exact spelling): SaaS (Software as a Service), Mobile App, Web Application, Desktop Software, Plugin/Extension, API/SDK, AI/ML Solution, IoT Device, Gaming Application, ERP, Learning Management System (LMS), Cybersecurity Solutions, Chatbot Integration.\n" +
            "Platforms for matching (separate with commas if multiple): Web, Android, iOS, etc."; // this the main prompt

    private final GeminiClient geminiClient;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    @Retryable(
        maxAttempts = 3,
        backoff = @Backoff(
            delay = 1000,
            multiplier = 2,
            maxDelay = 10000
        )
    )
    public CompanyDataModel scrapeFromWebsite(String url) throws Exception {
        try {
            String jsonStructure = new BeanOutputConverter<>(WebsiteInfo.class).getFormat();

            StringBuilder sb = new StringBuilder();
            sb.append(SYSTEM_PROMPT);
            sb.append(jsonStructure);
            sb.append("URL: {}").append(url);

            GeminiRequest geminiRequest = GeminiRequest.builder()
                    .contents(List.of(
                            GeminiRequest.Content.builder()
                                    .parts(
                                            List.of(GeminiRequest.Part.builder()
                                                    .text(sb.toString())
                                                    .build())
                                    )
                                    .build()
                    ))
                    .build();

            GeminiResponse response = geminiClient.generateContent("gemini-2.5-flash-preview-04-17", "AIzaSyBZW8zg-8g8Uul33M5zUm8n-zCfXwkBjaA", geminiRequest);

            // Process the response and convert to CompanyDataModel
            if (response != null && response.getCandidates() != null && !response.getCandidates().isEmpty()) {
                String jsonContent = response.getCandidates().get(0).getContent().getParts().get(0).getText();
                WebsiteInfo websiteInfo = processWebsiteInfoJson(jsonContent);

                return convertToCompanyDataModel(websiteInfo, url);
            }

            return CompanyDataModel.failed("website", "No response from Gemini API");
        } catch (Exception e) {
            log.error("Error scraping website: {}", url, e);
            throw e; // Let the retry mechanism handle it
        }
    }

    @Recover
    public CompanyDataModel fallback(Exception e, String url) {
        log.error("All retry attempts failed for website scraping: {}", url, e);
        return CompanyDataModel.failed("website", 
            String.format("Failed after all retry attempts. Last error: %s", e.getMessage()));
    }

    /**
     * Process and clean the JSON response to create a properly mapped WebsiteInfo object
     */
    private WebsiteInfo processWebsiteInfoJson(String jsonContent) throws Exception {
        // Clean the JSON content first
        String cleanedJson = cleanJsonString(jsonContent);
        JsonNode jsonNode = objectMapper.readTree(cleanedJson);

        WebsiteInfo.WebsiteInfoBuilder builder = WebsiteInfo.builder();

        // Basic company information
        if (jsonNode.has("name")) {
            builder.name(jsonNode.get("name").asText());
        }
        if (jsonNode.has("url")) {
            builder.url(jsonNode.get("url").asText());
        }
        if (jsonNode.has("headquartersLocation")) {
            builder.country(jsonNode.get("headquartersLocation").asText());
        }
        if (jsonNode.has("foundedYear")) {
            JsonNode foundedYearNode = jsonNode.get("foundedYear");
            if (foundedYearNode.isInt()) {
                builder.foundedYear(foundedYearNode.asInt());
            } else if (foundedYearNode.isTextual()) {
                String foundedYearText = foundedYearNode.asText();
                Pattern pattern = Pattern.compile("\\d{4}");
                Matcher matcher = pattern.matcher(foundedYearText);
                if (matcher.find()) {
                    builder.foundedYear(Integer.parseInt(matcher.group(0)));
                }
            }
        }
        if (jsonNode.has("employeeSize")) {
            builder.employeeSize(jsonNode.get("employeeSize").asText());
        }
        if (jsonNode.has("fundingStatus")) {
            builder.fundingStatus(jsonNode.get("fundingStatus").asText());
        }
        if (jsonNode.has("focusStatement")) {
            builder.focusStatement(jsonNode.get("focusStatement").asText());
        }
        if (jsonNode.has("description")) {
            builder.cultureDescription(jsonNode.get("description").asText());
        }
        if (jsonNode.has("cultureDescription")) {
            builder.cultureDescription(jsonNode.get("cultureDescription").asText());
        }
        if (jsonNode.has("outcomeSummary")) {
            builder.outcomeSummary(jsonNode.get("outcomeSummary").asText());
        }

        // Process aliases
        List<String> aliases = new ArrayList<>();
        if (jsonNode.has("aliases") && jsonNode.get("aliases").isArray()) {
            jsonNode.get("aliases").forEach(alias -> aliases.add(alias.asText()));
        }
        builder.aliases(aliases);

        // Process other info
        if (jsonNode.has("otherInfo")) {
            JsonNode otherInfoNode = jsonNode.get("otherInfo");
            WebsiteInfo.OtherCompanyInfo.OtherCompanyInfoBuilder otherInfoBuilder = WebsiteInfo.OtherCompanyInfo.builder();

            if (otherInfoNode.has("foundingStory")) {
                otherInfoBuilder.foundingStory(otherInfoNode.get("foundingStory").asText());
            }

            // Process global presence
            List<String> globalPresence = new ArrayList<>();
            if (otherInfoNode.has("globalPresence") && otherInfoNode.get("globalPresence").isArray()) {
                otherInfoNode.get("globalPresence").forEach(location -> globalPresence.add(location.asText()));
            }
            otherInfoBuilder.globalPresence(globalPresence);

            // Process initiatives
            List<String> initiatives = new ArrayList<>();
            if (otherInfoNode.has("initiatives") && otherInfoNode.get("initiatives").isArray()) {
                otherInfoNode.get("initiatives").forEach(initiative -> initiatives.add(initiative.asText()));
            }
            otherInfoBuilder.initiatives(initiatives);

            builder.otherInfo(otherInfoBuilder.build());
        }

        // Process industries
        List<Industry> industries = new ArrayList<>();
        if (jsonNode.has("industries") && jsonNode.get("industries").isArray()) {
            jsonNode.get("industries").forEach(industryNode -> {
                Industry industry = Industry.builder()
                        .industryId(parseUuidSafely(industryNode, "industryId"))
                        .name(industryNode.get("name").asText())
                        .build();
                industries.add(industry);
            });
        }
        builder.industries(industries);

        // Process product services
        List<ProductService> productServices = new ArrayList<>();
        if (jsonNode.has("productServices") && jsonNode.get("productServices").isArray()) {
            jsonNode.get("productServices").forEach(serviceNode -> {
                ProductService.ProductServiceBuilder serviceBuilder = ProductService.builder()
                        .productServiceId(parseUuidSafely(serviceNode, "productServiceId"))
                        .name(serviceNode.get("name").asText())
                        .type(serviceNode.get("type").asText())
                        .description(serviceNode.get("description").asText());

                // Process features for this service
                List<Feature> features = new ArrayList<>();
                if (serviceNode.has("features") && serviceNode.get("features").isArray()) {
                    serviceNode.get("features").forEach(featureNode -> {
                        Feature feature = Feature.builder()
                                .featureId(parseUuidSafely(featureNode, "featureId"))
                                .name(featureNode.get("name").asText())
                                .description(featureNode.has("description") && !featureNode.get("description").isNull() ?
                                        featureNode.get("description").asText() : null)
                                .build();
                        features.add(feature);
                    });
                }
                serviceBuilder.features(features);

                productServices.add(serviceBuilder.build());
            });
        }
        builder.productServices(productServices);

        // Process achievements
        List<Achievement> achievements = new ArrayList<>();
        if (jsonNode.has("achievements") && jsonNode.get("achievements").isArray()) {
            jsonNode.get("achievements").forEach(achievementNode -> {
                Achievement achievement = Achievement.builder()
                        .achievementId(parseUuidSafely(achievementNode, "achievementId"))
                        .name(achievementNode.get("name").asText())
                        .type(achievementNode.get("type").asText())
                        .description(achievementNode.get("description").asText())
                        .year(achievementNode.has("year") && !achievementNode.get("year").isNull() ?
                                achievementNode.get("year").asInt() : null)
                        .sourceURL(achievementNode.has("sourceURL") && !achievementNode.get("sourceURL").isNull() ?
                                achievementNode.get("sourceURL").asText() : null)
                        .build();
                achievements.add(achievement);
            });
        }
        builder.achievements(achievements);

        return builder.build();
    }

    /**
     * Clean JSON string by removing markdown formatting like ```json and ```
     */
    private String cleanJsonString(String jsonContent) {
        if (jsonContent == null || jsonContent.trim().isEmpty()) {
            return jsonContent;
        }

        // Remove markdown code block formatting
        String cleaned = jsonContent.trim();

        // Remove starting ```json or ``` 
        if (cleaned.startsWith("```json")) {
            cleaned = cleaned.substring(7);
        } else if (cleaned.startsWith("```")) {
            cleaned = cleaned.substring(3);
        }

        // Remove ending ```
        if (cleaned.endsWith("```")) {
            cleaned = cleaned.substring(0, cleaned.length() - 3);
        }

        return cleaned.trim();
    }

    /**
     * Safely parse UUID from JsonNode, returns UUID.randomUUID() if parsing fails
     */
    private UUID parseUuidSafely(JsonNode node, String fieldName) {
        try {
            if (node.has(fieldName) && !node.get(fieldName).isNull()) {
                String uuidString = node.get(fieldName).asText();
                if (uuidString != null && !uuidString.trim().isEmpty() && !uuidString.equals("null")) {
                    return UUID.fromString(uuidString);
                }
            }
        } catch (IllegalArgumentException e) {
            log.warn("Failed to parse UUID from field '{}' with value '{}': {}",
                    fieldName, node.has(fieldName) ? node.get(fieldName).asText() : "missing", e.getMessage());
        }
        return UUID.randomUUID();
    }

    /**
     * Convert WebsiteInfo to CompanyDataModel for consistency with other scrapers
     */
    private CompanyDataModel convertToCompanyDataModel(WebsiteInfo websiteInfo, String url) {
        CompanyDataModel.CompanyDataModelBuilder builder = CompanyDataModel.builder()
                .source("website")
                .isSuccessful(true)
                .scrapedAt(LocalDateTime.now())
                .name(websiteInfo.getName() != null ? websiteInfo.getName() : "")
                .description(websiteInfo.getCultureDescription() != null ? websiteInfo.getCultureDescription() : "")
                .clutchFoundedYear(websiteInfo.getFoundedYear() != null ? websiteInfo.getFoundedYear().toString() : "")
                .clutchCompanySize(websiteInfo.getEmployeeSize() != null ? websiteInfo.getEmployeeSize() : "")
                .clutchCompanyUrl(url)
                .websiteTitle(websiteInfo.getFocusStatement() != null ? websiteInfo.getFocusStatement() : "")
                .websiteDescription(websiteInfo.getCultureDescription() != null ? websiteInfo.getCultureDescription() : "");

        // Add headquarters location
        if (websiteInfo.getCountry() != null) {
            builder.clutchLocation(websiteInfo.getCountry());
            String[] locationParts = websiteInfo.getCountry().split(",");
            if (locationParts.length >= 2) {
                builder.clutchCity(locationParts[0].trim())
                        .clutchCountry(locationParts[locationParts.length - 1].trim());
            } else {
                builder.clutchCity(websiteInfo.getCountry());
            }
        } else {
            builder.clutchLocation("").clutchCity("").clutchCountry("");
        }

        // Add services as a comma-separated string
        if (websiteInfo.getProductServices() != null && !websiteInfo.getProductServices().isEmpty()) {
            List<String> serviceNames = websiteInfo.getProductServices().stream()
                    .map(ProductService::getName)
                    .toList();
        }

        // Add industries as a comma-separated string
        if (websiteInfo.getIndustries() != null && !websiteInfo.getIndustries().isEmpty()) {
            List<String> industryNames = websiteInfo.getIndustries().stream()
                    .map(Industry::getName)
                    .toList();
        }

        // Store the complete WebsiteInfo as JSON for later processing by DataExtractionDelegate
        CompanyDataModel dataModel = builder.build();

        // Add custom website-specific data for DataExtractionDelegate to process
        try {
            String websiteInfoJson = objectMapper.writeValueAsString(websiteInfo);
            dataModel.setWebsiteKeywords(websiteInfoJson); // Temporarily store full JSON here
        } catch (Exception e) {
            log.warn("Failed to serialize WebsiteInfo to JSON: {}", e.getMessage());
        }

        return dataModel;
    }

    /**
     * Extract WebsiteInfo from CompanyDataModel (used by DataExtractionDelegate)
     */
    public static WebsiteInfo extractWebsiteInfoFromCompanyDataModel(CompanyDataModel dataModel) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(dataModel.getWebsiteKeywords(), WebsiteInfo.class);
        } catch (Exception e) {
            // If JSON parsing fails, create basic WebsiteInfo from available data
            return WebsiteInfo.builder()
                    .name(dataModel.getName())
                    .url(dataModel.getClutchCompanyUrl())
                    .cultureDescription(dataModel.getDescription())
                    .foundedYear(dataModel.getClutchFoundedYear() != null ? Integer.parseInt(dataModel.getClutchFoundedYear()) : null)
                    .employeeSize(dataModel.getClutchCompanySize())
                    .country(dataModel.getClutchLocation())
                    .focusStatement(dataModel.getWebsiteTitle())
                    .build();
        }
    }
}
