package com.enosisbd.app.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties for the scraping scheduler
 */
@Configuration
@ConfigurationProperties(prefix = "scraping.scheduler")
@Getter
@Setter
public class SchedulingConfig {

    /**
     * Minimum gap between scraping operations in minutes
     */
    private int minGapMinutes = 30;

    /**
     * Maximum additional random minutes to add to the minimum gap
     */
    private int maxAdditionalRandomMinutes = 30;

    /**
     * Maximum number of concurrent scraping operations
     */
    private int maxConcurrentProcessing = 1;

    /**
     * Default maximum retry attempts for failed scraping operations
     */
    private int defaultMaxRetries = 3;

    /**
     * Interval for running the scheduling job in minutes
     */
    private int schedulingJobIntervalMinutes = 5;

    /**
     * Interval for running the processing job in minutes
     */
    private int processingJobIntervalMinutes = 2;

    /**
     * Interval for running maintenance job in minutes
     */
    private int maintenanceJobIntervalMinutes = 30;

    /**
     * Initial delay before starting scheduling job in minutes
     */
    private int schedulingJobInitialDelayMinutes = 2;

    /**
     * Initial delay before starting processing job in minutes
     */
    private int processingJobInitialDelayMinutes = 1;

    /**
     * Initial delay before starting maintenance job in minutes
     */
    private int maintenanceJobInitialDelayMinutes = 10;
} 