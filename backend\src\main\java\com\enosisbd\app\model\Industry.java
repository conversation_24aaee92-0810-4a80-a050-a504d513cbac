package com.enosisbd.app.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

// --- Industry ---
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Industry {
    private UUID industryId;
    private String name;

    // For Many-to-Many relationship with Company (managed through CompanyIndustry table)
    // In a real JPA/Hibernate setup, this would often be mapped with @ManyToMany
    private List<WebsiteInfo> companies = new ArrayList<>();
}
