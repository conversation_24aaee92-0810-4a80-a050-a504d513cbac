package com.enosisbd.app.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

// --- ProductServicePlatform (Junction Table) ---
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductServicePlatform {
    private UUID productServiceId; // Foreign Key to ProductService
    private UUID platformId; // Foreign Key to Platform
}
