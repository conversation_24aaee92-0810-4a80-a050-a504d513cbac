<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="686e1f5a-4a9d-4f55-90e5-ea45d62bb165" name="Changes" comment="Integrated Frontend and Backend. &#10;&#10;Supports File Based Downloading and Processing.&#10;&#10;Project: EOS TOOLS. #Integration.">
      <change afterPath="$PROJECT_DIR$/.idea/jpa.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/compiler.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/compiler.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/controller/ScraperController.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/controller/ScraperController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/entity/CompanyEntity.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/entity/CompanyEntity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/flowable/DataExtractionDelegate.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/flowable/DataExtractionDelegate.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/gemini/GeminiClient.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/gemini/GeminiClient.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/model/CompanyDataModel.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/model/CompanyDataModel.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/model/WebsiteInfo.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/model/WebsiteInfo.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/repository/CompanyRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/repository/CompanyRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/repository/ScrapeRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/repository/ScrapeRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/FileWatcherService.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/FileWatcherService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/WebsiteScraperService.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/WebsiteScraperService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/impl/ScrapeSchedulerServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/impl/ScrapeSchedulerServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/impl/ScraperServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/impl/ScraperServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/impl/clutch/ClutchScraperServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/impl/clutch/ClutchScraperServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/impl/glassdoor/GlassdoorScraperServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/impl/glassdoor/GlassdoorScraperServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/impl/website/WebsiteCrawlerServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/impl/website/WebsiteCrawlerServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/resources/application.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/resources/application.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/src/components/dashboard/scraping-jobs.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/src/components/dashboard/scraping-jobs.tsx" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="JpbToolWindowState">
    <option name="isToolWindowVisible" value="false" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2z4pIsgigg39IpSEnOsU8JZ2zeb" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.Application.executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/EOS/eos&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="Application" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.enosisbd.app.Application" />
      <module name="app" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.enosisbd.app.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.Application" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="686e1f5a-4a9d-4f55-90e5-ea45d62bb165" name="Changes" comment="" />
      <created>1751000122785</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751000122785</updated>
    </task>
    <task id="LOCAL-00001" summary="Added Backend Part.">
      <option name="closed" value="true" />
      <created>1751000201306</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751000201306</updated>
    </task>
    <task id="LOCAL-00002" summary="Frontend Cleared.">
      <option name="closed" value="true" />
      <created>1751001919858</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1751001919858</updated>
    </task>
    <task id="LOCAL-00003" summary="Integrated Frontend and Backend. &#10;&#10;Currently, Frontend will upload a CSV file and Backend will process and store the data in the file system.&#10;&#10;Project: EOS TOOLS. #Integration.">
      <option name="closed" value="true" />
      <created>1751029468246</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1751029468246</updated>
    </task>
    <task id="LOCAL-00004" summary="Integrated Frontend and Backend. &#10;&#10;Supports File Based Downloading and Processing.&#10;&#10;Project: EOS TOOLS. #Integration.">
      <option name="closed" value="true" />
      <created>1751035022020</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1751035022020</updated>
    </task>
    <option name="localTasksCounter" value="5" />
    <servers />
  </component>
  <component name="UnknownFeatures">
    <option featureType="com.intellij.fileTypeFactory" implementationName="application.yaml" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="Added Backend Part." />
    <MESSAGE value="Frontend Cleared." />
    <MESSAGE value="Integrated Frontend and Backend. &#10;&#10;Currently, Frontend will upload a CSV file and Backend will process and store the data in the file system.&#10;&#10;Project: EOS TOOLS. #Integration." />
    <MESSAGE value="Integrated Frontend and Backend. &#10;&#10;Supports File Based Downloading and Processing.&#10;&#10;Project: EOS TOOLS. #Integration." />
    <option name="LAST_COMMIT_MESSAGE" value="Integrated Frontend and Backend. &#10;&#10;Supports File Based Downloading and Processing.&#10;&#10;Project: EOS TOOLS. #Integration." />
  </component>
</project>