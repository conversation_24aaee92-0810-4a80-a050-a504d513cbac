package com.enosisbd.app.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

// --- ProductServiceFeature (Junction Table) ---
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductServiceFeature {
    private UUID productServiceId; // Foreign Key to ProductService
    private UUID featureId; // Foreign Key to Feature
}
