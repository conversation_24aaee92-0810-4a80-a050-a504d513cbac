# EOS Dashboard Structure

This document outlines the comprehensive dashboard layout structure for the EOS (Enosis Solutions) project.

## Overview

The dashboard is built using React with TypeScript and shadcn/ui components, providing a modern, responsive interface for managing scraping operations, viewing company data, and monitoring system analytics.

## Project Structure

```
src/
├── components/
│   ├── layout/                 # Layout components
│   │   ├── root-layout.tsx     # Main layout wrapper
│   │   ├── sidebar.tsx         # Navigation sidebar
│   │   ├── header.tsx          # Top header with search and user menu
│   │   └── index.ts            # Layout exports
│   ├── dashboard/              # Dashboard-specific components
│   │   ├── dashboard-overview.tsx    # Main dashboard overview
│   │   ├── companies-table.tsx       # Companies management table
│   │   ├── scraping-jobs.tsx         # Scraping jobs management
│   │   ├── analytics-dashboard.tsx   # Analytics and charts
│   │   ├── settings-panel.tsx        # Settings and configuration
│   │   └── index.ts            # Dashboard exports
│   └── ui/                     # shadcn/ui components
└── App.tsx                     # Main application component
```

## Components Overview

### Layout Components

#### RootLayout (`components/layout/root-layout.tsx`)
- Main layout wrapper that provides the overall structure
- Includes sidebar and header
- Handles responsive layout and overflow

#### Sidebar (`components/layout/sidebar.tsx`)
- Navigation sidebar with main menu items
- Includes company management, scraping jobs, analytics, reports, users, and settings
- Responsive design with collapsible functionality
- User profile and logout options

#### Header (`components/layout/header.tsx`)
- Top header with search functionality
- User profile dropdown with settings and logout
- Notification bell with badge
- Responsive design

### Dashboard Components

#### DashboardOverview (`components/dashboard/dashboard-overview.tsx`)
- Main dashboard with key metrics and statistics
- Metric cards showing total companies, active jobs, users, and data points
- Job status overview with progress indicators
- Recent activity feed
- Responsive grid layout

#### CompaniesTable (`components/dashboard/companies-table.tsx`)
- Comprehensive table for managing company data
- Search and filtering capabilities
- Status badges and action menus
- Pagination support
- Export functionality

#### ScrapingJobs (`components/dashboard/scraping-jobs.tsx`)
- Job management interface
- Create new scraping jobs with dialog
- Monitor job status and progress
- Job metrics and statistics
- Action menus for job control

#### AnalyticsDashboard (`components/dashboard/analytics-dashboard.tsx`)
- Comprehensive analytics with tabs
- Industry analysis charts
- Data source performance metrics
- Growth trends visualization
- Export capabilities

#### SettingsPanel (`components/dashboard/settings-panel.tsx`)
- Multi-tab settings interface
- Profile management
- Scraping configuration
- Notification preferences
- Security settings
- Advanced system settings

## Features

### Key Features
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Modern UI**: Built with shadcn/ui components for consistent design
- **Real-time Updates**: Live status indicators and progress tracking
- **Search & Filter**: Advanced search and filtering capabilities
- **Export Functionality**: Data export in various formats
- **User Management**: Role-based access control
- **Analytics**: Comprehensive data visualization and reporting

### Navigation Structure
1. **Dashboard** - Overview and key metrics
2. **Companies** - Company data management
3. **Scraping Jobs** - Job monitoring and control
4. **Analytics** - Data analysis and reporting
5. **Reports** - Generated reports and exports
6. **Users** - User management
7. **Settings** - System configuration

### Data Sources
- **Clutch** - Company reviews and ratings
- **Glassdoor** - Employee reviews and company insights
- **Goodfirms** - Software and service reviews

## Usage

### Basic Setup
```tsx
import { RootLayout } from './components/layout'
import { DashboardOverview } from './components/dashboard'

function App() {
  return (
    <RootLayout>
      <DashboardOverview />
    </RootLayout>
  )
}
```

### Component Usage
```tsx
// Dashboard Overview
<DashboardOverview />

// Companies Table
<CompaniesTable />

// Scraping Jobs
<ScrapingJobs />

// Analytics Dashboard
<AnalyticsDashboard />

// Settings Panel
<SettingsPanel />
```

## Styling

The dashboard uses:
- **Tailwind CSS** for utility-first styling
- **shadcn/ui** for consistent component design
- **Lucide React** for icons
- **CSS Variables** for theming

## Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Development

### Prerequisites
- Node.js 18+
- npm or yarn
- React 18+
- TypeScript 5+

### Installation
```bash
npm install
```

### Development Server
```bash
npm run dev
```

### Build
```bash
npm run build
```

## Customization

### Adding New Pages
1. Create new component in `components/dashboard/`
2. Add navigation item to `components/layout/sidebar.tsx`
3. Update routing if needed
4. Export from `components/dashboard/index.ts`

### Theming
- Modify CSS variables in `src/index.css`
- Update color schemes in `components.json`
- Customize component styles in individual files

### Data Integration
- Replace mock data with API calls
- Implement real-time updates
- Add error handling and loading states

## Performance Considerations

- Lazy loading for large components
- Virtual scrolling for large tables
- Debounced search inputs
- Optimized re-renders with React.memo
- Efficient state management

## Security

- Role-based access control
- Input validation
- XSS protection
- Secure API communication
- Session management

## Future Enhancements

- Real-time WebSocket updates
- Advanced charting with D3.js
- Mobile app version
- API documentation integration
- Advanced filtering and sorting
- Bulk operations
- Data import functionality
- Advanced analytics with ML insights 