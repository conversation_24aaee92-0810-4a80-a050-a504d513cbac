package com.enosisbd.app.service.crawler;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Utility class for generating realistic HTTP headers for web scraping
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class HeaderGenerator {

    private final UserAgentRotator userAgentRotator;
    private final Random random = new Random();

    // Common language codes
    private static final String[] LANGUAGE_CODES = {
            "en-US", "en-GB", "en-CA", "fr-FR", "de-DE", "es-ES", "it-IT", "ja-JP", 
            "ko-KR", "zh-CN", "zh-TW", "ru-RU", "pt-BR", "nl-NL", "pl-PL", "tr-TR"
    };

    // Common encoding types
    private static final String[] ENCODING_TYPES = {
            "gzip, deflate, br", "gzip, deflate", "br", "gzip"
    };

    /**
     * Generate a map of realistic HTTP headers
     * 
     * @param isMobile Whether to use mobile user agent
     * @param referrer Optional referrer URL
     * @return Map of HTTP header name to value
     */
    public Map<String, String> generateHeaders(boolean isMobile, String referrer) {
        Map<String, String> headers = new HashMap<>();
        
        // User-Agent
        String userAgent = userAgentRotator.getRandomUserAgent(isMobile);
        headers.put("User-Agent", userAgent);
        
        // Accept header
        headers.put("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8");
        
        // Accept-Language
        String languageCode = LANGUAGE_CODES[random.nextInt(LANGUAGE_CODES.length)];
        headers.put("Accept-Language", languageCode + ",en;q=0.9");
        
        // Accept-Encoding
        String encoding = ENCODING_TYPES[random.nextInt(ENCODING_TYPES.length)];
        headers.put("Accept-Encoding", encoding);
        
        // Connection
        headers.put("Connection", random.nextBoolean() ? "keep-alive" : "close");
        
        // Upgrade-Insecure-Requests
        headers.put("Upgrade-Insecure-Requests", "1");
        
        // Sec-Fetch headers (modern browsers)
        if (random.nextBoolean()) {
            headers.put("Sec-Fetch-Dest", "document");
            headers.put("Sec-Fetch-Mode", "navigate");
            headers.put("Sec-Fetch-Site", referrer != null ? "same-origin" : "none");
            headers.put("Sec-Fetch-User", "?1");
        }
        
        // Referrer
        if (referrer != null && !referrer.isEmpty()) {
            headers.put("Referer", referrer);
        }
        
        // Cache-Control
        if (random.nextBoolean()) {
            headers.put("Cache-Control", "max-age=0");
        }
        
        // DNT (Do Not Track)
        if (random.nextBoolean()) {
            headers.put("DNT", "1");
        }
        
        return headers;
    }

    /**
     * Apply headers to a Playwright page
     * 
     * @param page The Playwright page
     * @param isMobile Whether to use mobile user agent
     * @param referrer Optional referrer URL
     */
    public void applyHeadersToPage(com.microsoft.playwright.Page page, boolean isMobile, String referrer) {
        Map<String, String> headers = generateHeaders(isMobile, referrer);
        page.setExtraHTTPHeaders(headers);
        log.debug("Applied {} headers to page", headers.size());
    }

    /**
     * Generate a random viewport size
     *
     * @param isMobile Whether to generate a mobile viewport
     * @return int array with [width, height]
     */
    public int[] generateRandomViewport(boolean isMobile) {
        int width, height;
        
        if (isMobile) {
            // Mobile viewport sizes
            int[] commonWidths = {320, 360, 375, 390, 414, 428};
            int[] commonHeights = {568, 640, 667, 736, 812, 844, 896, 926};
            
            width = commonWidths[random.nextInt(commonWidths.length)];
            height = commonHeights[random.nextInt(commonHeights.length)];
        } else {
            // Desktop viewport sizes
            width = 1200 + random.nextInt(720); // Between 1200 and 1920
            height = 800 + random.nextInt(280); // Between 800 and 1080
        }
        
        return new int[]{width, height};
    }
}
