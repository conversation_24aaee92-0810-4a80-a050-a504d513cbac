package com.enosisbd.app.service.crawler;

import java.util.List;
import java.util.Map;

/**
 * Generic crawler service interface for flexible web scraping
 */
public interface GenericCrawlerService {
    
    /**
     * Scrape content from a URL using CSS selectors
     * 
     * @param url The URL to scrape
     * @param selectors Map of label to CSS selector
     * @param options Optional scraping options
     * @return Map of label to scraped content
     */
    Map<String, String> scrapeWithSelectors(String url, Map<String, String> selectors, Map<String, Object> options);
    
    /**
     * Scrape content from a URL using XPath expressions
     * 
     * @param url The URL to scrape
     * @param xpaths Map of label to XPath expression
     * @param options Optional scraping options
     * @return Map of label to scraped content
     */
    Map<String, String> scrapeWithXPath(String url, Map<String, String> xpaths, Map<String, Object> options);
    
    /**
     * Scrape content from a URL using both CSS selectors and XPath expressions
     * 
     * @param url The URL to scrape
     * @param selectors Map of label to CSS selector
     * @param xpaths Map of label to XPath expression
     * @param options Optional scraping options
     * @return Map of label to scraped content
     */
    Map<String, String> scrapeWithMixedSelectors(String url, Map<String, String> selectors, 
                                                Map<String, String> xpaths, Map<String, Object> options);
    
    /**
     * Scrape content from a URL and extract children elements
     * 
     * @param url The URL to scrape
     * @param parentSelector The CSS selector for the parent element
     * @param childSelectors Map of label to CSS selector relative to parent
     * @param options Optional scraping options
     * @return List of maps, each containing the scraped content for one parent element
     */
    List<Map<String, String>> scrapeChildContent(String url, String parentSelector, 
                                               Map<String, String> childSelectors, Map<String, Object> options);
    
    /**
     * Scrape content from a URL and extract children elements using XPath
     * 
     * @param url The URL to scrape
     * @param parentXPath The XPath expression for the parent element
     * @param childXPaths Map of label to XPath expression relative to parent
     * @param options Optional scraping options
     * @return List of maps, each containing the scraped content for one parent element
     */
    List<Map<String, String>> scrapeChildContentWithXPath(String url, String parentXPath, 
                                                        Map<String, String> childXPaths, Map<String, Object> options);
    
    /**
     * Scrape content from a URL with pagination support
     * 
     * @param baseUrl The base URL to scrape
     * @param paginationPattern The pattern for pagination URLs
     * @param startPage The starting page number
     * @param maxPages The maximum number of pages to scrape
     * @param selectors Map of label to CSS selector
     * @param options Optional scraping options
     * @return List of maps, each containing the scraped content for one page
     */
    List<Map<String, String>> scrapeWithPagination(String baseUrl, String paginationPattern, int startPage, 
                                                 int maxPages, Map<String, String> selectors, Map<String, Object> options);
    
    /**
     * Scrape content that requires login
     * 
     * @param loginUrl The login page URL
     * @param targetUrl The target URL to scrape after login
     * @param loginCredentials Map containing login credentials (username, password, etc.)
     * @param selectors Map of label to CSS selector
     * @param options Optional scraping options
     * @return Map of label to scraped content
     */
    Map<String, String> scrapeWithLogin(String loginUrl, String targetUrl, Map<String, String> loginCredentials, 
                                      Map<String, String> selectors, Map<String, Object> options);
    
    /**
     * Scrape content from a URL that requires JavaScript rendering
     * 
     * @param url The URL to scrape
     * @param selectors Map of label to CSS selector
     * @param options Optional scraping options
     * @return Map of label to scraped content
     */
    Map<String, String> scrapeWithJavaScript(String url, Map<String, String> selectors, Map<String, Object> options);
    
    /**
     * Scrape content from multiple URLs in parallel
     * 
     * @param urls List of URLs to scrape
     * @param selectors Map of label to CSS selector
     * @param options Optional scraping options
     * @return List of maps, each containing the scraped content for one URL
     */
    List<Map<String, String>> scrapeMultipleUrls(List<String> urls, Map<String, String> selectors, Map<String, Object> options);
}
