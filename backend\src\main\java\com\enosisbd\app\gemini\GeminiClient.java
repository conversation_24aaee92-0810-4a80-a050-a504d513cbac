package com.enosisbd.app.gemini;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "geminiClient", url = "https://generativelanguage.googleapis.com")
public interface GeminiClient {
    
    @Retryable(
        maxAttempts = 3,
        backoff = @Backoff(
            delay = 1000,
            multiplier = 2.0,
            maxDelay = 10000
        )
    )
    @PostMapping(
            value = "/v1beta/models/{modelName}:generateContent",
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    GeminiResponse generateContent(
            @PathVariable("modelName") String modelName,
            @RequestParam("key") String apiKey,
            @RequestBody GeminiRequest request
    );

    @Recover
    default GeminiResponse fallback(Exception e, String modelName, String apiKey, GeminiRequest request) {
        // Create a fallback response indicating failure
        return GeminiResponse.builder()
                .candidates(null)
                .build();
    }
}