package com.enosisbd.app.scheduler;

import java.util.concurrent.TimeUnit;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.enosisbd.app.service.ScrapeSchedulerService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Scheduled job component for managing scrape operations
 * Runs periodically to schedule and process scrape items
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ScrapeScheduledJob {

    private final ScrapeSchedulerService scrapeSchedulerService;

    /**
     * Runs every 5 seconds to schedule ready items (fast for testing)
     * This method schedules QUEUE items with randomized timing
     */
    @Scheduled(fixedDelay = 5, timeUnit = TimeUnit.SECONDS, initialDelay = 2)
    public void scheduleReadyItems() {
        log.debug("Running scheduled job: scheduleReadyItems");
        
        try {
            scrapeSchedulerService.scheduleReadyItems();
        } catch (Exception e) {
            log.error("Error in scheduleReadyItems job: {}", e.getMessage(), e);
        }
    }

    /**
     * Runs every 10 seconds to process scheduled items (fast for testing)
     * This method starts processing for items whose scheduled time has arrived
     */
    @Scheduled(fixedDelay = 10, timeUnit = TimeUnit.SECONDS, initialDelay = 5)
    public void processScheduledItems() {
        log.debug("Running scheduled job: processScheduledItems");
        
        try {
            scrapeSchedulerService.processScheduledItems();
        } catch (Exception e) {
            log.error("Error in processScheduledItems job: {}", e.getMessage(), e);
        }
    }

    /**
     * Runs every 2 minutes to perform maintenance tasks
     * This can include cleanup, metrics collection, etc.
     */
    @Scheduled(fixedDelay = 2, timeUnit = TimeUnit.MINUTES, initialDelay = 30)
    public void performMaintenance() {
        log.debug("Running scheduled maintenance job");
        
        try {
            // Add any maintenance tasks here
            log.info("Scheduled maintenance completed");
        } catch (Exception e) {
            log.error("Error in maintenance job: {}", e.getMessage(), e);
        }
    }
} 