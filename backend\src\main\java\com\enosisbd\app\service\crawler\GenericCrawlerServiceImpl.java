package com.enosisbd.app.service.crawler;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.microsoft.playwright.Browser;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.BrowserType;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.WaitUntilState;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Implementation of the GenericCrawlerService interface with enhanced anti-detection features.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GenericCrawlerServiceImpl implements GenericCrawlerService {

    private final UserAgentRotator userAgentRotator;
    private final HeaderGenerator headerGenerator;

    @Value("${scraping.timeout-ms:120000}")
    private int timeoutMs;

    @Value("${scraping.anti-detection.enabled:true}")
    private boolean antiDetectionEnabled;

    @Value("${scraping.anti-detection.random-delays:true}")
    private boolean useRandomDelays;

    @Value("${scraping.anti-detection.min-delay-ms:1500}")
    private int minDelayMs;

    @Value("${scraping.anti-detection.max-delay-ms:4000}")
    private int maxDelayMs;

    @Value("${scraping.max-retries:3}")
    private int maxRetries;

    @Value("${scraping.retry-delay-ms:5000}")
    private int retryDelayMs;

    @Value("${scraping.parallel-requests:5}")
    private int parallelRequests;

    private final Random random = new Random();

    @Override
    public Map<String, String> scrapeWithSelectors(String url, Map<String, String> selectors, Map<String, Object> options) {
        Map<String, Object> mergedOptions = mergeWithDefaultOptions(options);

        try (Playwright playwright = Playwright.create()) {
            Browser browser = createBrowser(playwright, mergedOptions);
            BrowserContext context = createBrowserContext(browser, mergedOptions);

            try {
                Page page = context.newPage();
                configurePage(page, mergedOptions);

                navigateToUrl(page, url, mergedOptions);

                // Execute pre-scraping hook if it exists (for human-like behavior)
                if (mergedOptions.containsKey("preScrapingHook")) {
                    ((Consumer<Page>) mergedOptions.get("preScrapingHook")).accept(page);
                }

                Map<String, String> results = new HashMap<>();
                for (Map.Entry<String, String> entry : selectors.entrySet()) {
                    String label = entry.getKey();
                    String selector = entry.getValue();

                    try {
                        Locator locator = page.locator(selector);
                        if (locator.count() > 0) {
                            String content = extractContent(locator, mergedOptions);
                            results.put(label, content);
                        } else {
                            results.put(label, "");
                            log.warn("Selector not found: {}", selector);
                        }
                    } catch (Exception e) {
                        log.error("Error extracting content for selector {}: {}", selector, e.getMessage());
                        results.put(label, "ERROR: " + e.getMessage());
                    }
                }

                return results;
            } finally {
                context.close();
            }
        } catch (Exception e) {
            log.error("Error scraping with selectors: {}", e.getMessage());
            return Map.of("error", e.getMessage());
        }
    }

    @Override
    public Map<String, String> scrapeWithXPath(String url, Map<String, String> xpaths, Map<String, Object> options) {
        Map<String, Object> mergedOptions = mergeWithDefaultOptions(options);

        try (Playwright playwright = Playwright.create()) {
            Browser browser = createBrowser(playwright, mergedOptions);
            BrowserContext context = createBrowserContext(browser, mergedOptions);

            try {
                Page page = context.newPage();
                configurePage(page, mergedOptions);

                // Navigate to the URL
                navigateToUrl(page, url, mergedOptions);



                // Extract content using XPath
                Map<String, String> results = new HashMap<>();
                for (Map.Entry<String, String> entry : xpaths.entrySet()) {
                    String label = entry.getKey();
                    String xpath = entry.getValue();

                    try {
                        // Use JavaScript to evaluate XPath
                        String content = (String) page.evaluate(
                            "xpath => { " +
                            "  const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null); " +
                            "  const node = result.singleNodeValue; " +
                            "  return node ? node.textContent.trim() : ''; " +
                            "}",
                            xpath
                        );

                        results.put(label, content);
                    } catch (Exception e) {
                        log.error("Error extracting content for XPath {}: {}", xpath, e.getMessage());
                        results.put(label, "ERROR: " + e.getMessage());
                    }
                }

                return results;
            } finally {
                context.close();
            }
        } catch (Exception e) {
            log.error("Error scraping with XPath: {}", e.getMessage());
            return Map.of("error", e.getMessage());
        }
    }

    @Override
    public Map<String, String> scrapeWithMixedSelectors(String url, Map<String, String> selectors,
                                                      Map<String, String> xpaths, Map<String, Object> options) {
        Map<String, String> results = new HashMap<>();

        // First scrape with CSS selectors
        if (selectors != null && !selectors.isEmpty()) {
            results.putAll(scrapeWithSelectors(url, selectors, options));
        }

        // Then scrape with XPath expressions
        if (xpaths != null && !xpaths.isEmpty()) {
            results.putAll(scrapeWithXPath(url, xpaths, options));
        }

        return results;
    }

    @Override
    public List<Map<String, String>> scrapeChildContent(String url, String parentSelector,
                                                     Map<String, String> childSelectors, Map<String, Object> options) {
        Map<String, Object> mergedOptions = mergeWithDefaultOptions(options);
        List<Map<String, String>> results = new ArrayList<>();

        try (Playwright playwright = Playwright.create()) {
            Browser browser = createBrowser(playwright, mergedOptions);
            BrowserContext context = createBrowserContext(browser, mergedOptions);

            try {
                Page page = context.newPage();
                configurePage(page, mergedOptions);

                // Navigate to the URL
                navigateToUrl(page, url, mergedOptions);


                // Get all parent elements
                Locator parentElements = page.locator(parentSelector);
                int count = parentElements.count();
                log.info("Found {} parent elements with selector: {}", count, parentSelector);

                // Extract content for each parent element
                for (int i = 0; i < count; i++) {
                    Locator parent = parentElements.nth(i);
                    Map<String, String> childResults = new HashMap<>();

                    for (Map.Entry<String, String> entry : childSelectors.entrySet()) {
                        String label = entry.getKey();
                        String childSelector = entry.getValue();

                        try {
                            Locator childElement = parent.locator(childSelector);
                            if (childElement.count() > 0) {
                                String content = extractContent(childElement, mergedOptions);
                                childResults.put(label, content);
                            } else {
                                childResults.put(label, "");
                                log.debug("Child selector not found: {} in parent {}", childSelector, i);
                            }
                        } catch (Exception e) {
                            log.error("Error extracting child content for selector {}: {}", childSelector, e.getMessage());
                            childResults.put(label, "ERROR: " + e.getMessage());
                        }
                    }

                    results.add(childResults);
                }

                return results;
            } finally {
                context.close();
            }
        } catch (Exception e) {
            log.error("Error scraping child content: {}", e.getMessage());
            return List.of(Map.of("error", e.getMessage()));
        }
    }

    @Override
    public List<Map<String, String>> scrapeChildContentWithXPath(String url, String parentXPath,
                                                              Map<String, String> childXPaths, Map<String, Object> options) {
        Map<String, Object> mergedOptions = mergeWithDefaultOptions(options);
        List<Map<String, String>> results = new ArrayList<>();

        try (Playwright playwright = Playwright.create()) {
            Browser browser = createBrowser(playwright, mergedOptions);
            BrowserContext context = createBrowserContext(browser, mergedOptions);

            try {
                Page page = context.newPage();
                configurePage(page, mergedOptions);

                // Navigate to the URL
                navigateToUrl(page, url, mergedOptions);

                // Use JavaScript to evaluate XPath and get parent nodes
                Object[] parentNodes = (Object[]) page.evaluate(
                    "xpath => { " +
                    "  const result = document.evaluate(xpath, document, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null); " +
                    "  const nodes = []; " +
                    "  for (let i = 0; i < result.snapshotLength; i++) { " +
                    "    nodes.push(i); " +  // We just push indices, as we can't pass DOM nodes
                    "  } " +
                    "  return nodes; " +
                    "}",
                    parentXPath
                );

                log.info("Found {} parent elements with XPath: {}", parentNodes.length, parentXPath);

                // For each parent index, extract child content
                for (Object parentIndex : parentNodes) {
                    Map<String, String> childResults = new HashMap<>();
                    int index = ((Double) parentIndex).intValue();

                    for (Map.Entry<String, String> entry : childXPaths.entrySet()) {
                        String label = entry.getKey();
                        String childXPath = entry.getValue();

                        try {
                            // Use JavaScript to evaluate relative XPath
                            String content = (String) page.evaluate(
                                "args => { " +
                                "  const [parentXPath, childXPath, index] = args; " +
                                "  const parentResult = document.evaluate(parentXPath, document, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null); " +
                                "  const parentNode = parentResult.snapshotItem(index); " +
                                "  if (!parentNode) return ''; " +
                                "  const childResult = document.evaluate(childXPath, parentNode, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null); " +
                                "  const childNode = childResult.singleNodeValue; " +
                                "  return childNode ? childNode.textContent.trim() : ''; " +
                                "}",
                                new String[]{parentXPath, childXPath, String.valueOf(index)}
                            );

                            childResults.put(label, content);
                        } catch (Exception e) {
                            log.error("Error extracting child content for XPath {}: {}", childXPath, e.getMessage());
                            childResults.put(label, "ERROR: " + e.getMessage());
                        }
                    }

                    results.add(childResults);
                }

                return results;
            } finally {
                context.close();
            }
        } catch (Exception e) {
            log.error("Error scraping child content with XPath: {}", e.getMessage());
            return List.of(Map.of("error", e.getMessage()));
        }
    }

    @Override
    public List<Map<String, String>> scrapeWithPagination(String baseUrl, String paginationPattern, int startPage,
                                                       int maxPages, Map<String, String> selectors, Map<String, Object> options) {
        List<Map<String, String>> allResults = new ArrayList<>();
        Map<String, Object> mergedOptions = mergeWithDefaultOptions(options);

        try (Playwright playwright = Playwright.create()) {
            Browser browser = createBrowser(playwright, mergedOptions);
            BrowserContext context = createBrowserContext(browser, mergedOptions);

            try {
                Page page = context.newPage();
                configurePage(page, mergedOptions);

                for (int currentPage = startPage; currentPage < startPage + maxPages; currentPage++) {
                    // Format the URL with the current page number
                    String url = String.format(paginationPattern, baseUrl, currentPage);
                    log.info("Scraping page {}: {}", currentPage, url);

                    try {
                        // Navigate to the URL
                        navigateToUrl(page, url, mergedOptions);

                        // Extract content using selectors
                        Map<String, String> results = new HashMap<>();
                        results.put("page_number", String.valueOf(currentPage));
                        results.put("page_url", url);

                        for (Map.Entry<String, String> entry : selectors.entrySet()) {
                            String label = entry.getKey();
                            String selector = entry.getValue();

                            try {
                                Locator locator = page.locator(selector);
                                if (locator.count() > 0) {
                                    String content = extractContent(locator, mergedOptions);
                                    results.put(label, content);
                                } else {
                                    results.put(label, "");
                                    log.warn("Selector not found on page {}: {}", currentPage, selector);
                                }
                            } catch (Exception e) {
                                log.error("Error extracting content for selector {} on page {}: {}",
                                        selector, currentPage, e.getMessage());
                                results.put(label, "ERROR: " + e.getMessage());
                            }
                        }

                        allResults.add(results);

                        // Check if there's a next page link
                        String nextPageSelector = (String) mergedOptions.getOrDefault("nextPageSelector", "");
                        if (!nextPageSelector.isEmpty()) {
                            boolean hasNextPage = page.locator(nextPageSelector).count() > 0;
                            if (!hasNextPage) {
                                log.info("No next page found, stopping pagination at page {}", currentPage);
                                break;
                            }
                        }

                        // Add delay between pages
                        if (useRandomDelays && currentPage < startPage + maxPages - 1) {
                            int delay = minDelayMs + random.nextInt(maxDelayMs - minDelayMs);
                            log.debug("Waiting {} ms before next page", delay);
                            page.waitForTimeout(delay);
                        }
                    } catch (Exception e) {
                        log.error("Error scraping page {}: {}", currentPage, e.getMessage());
                        Map<String, String> errorResult = new HashMap<>();
                        errorResult.put("page_number", String.valueOf(currentPage));
                        errorResult.put("page_url", url);
                        errorResult.put("error", e.getMessage());
                        allResults.add(errorResult);

                        // Decide whether to continue or stop
                        boolean continueOnError = (boolean) mergedOptions.getOrDefault("continueOnError", true);
                        if (!continueOnError) {
                            log.info("Stopping pagination due to error");
                            break;
                        }
                    }
                }

                return allResults;
            } finally {
                context.close();
            }
        } catch (Exception e) {
            log.error("Error in pagination scraping: {}", e.getMessage());
            return List.of(Map.of("error", e.getMessage()));
        }
    }

    @Override
    public Map<String, String> scrapeWithLogin(String loginUrl, String targetUrl, Map<String, String> loginCredentials,
                                            Map<String, String> selectors, Map<String, Object> options) {
        Map<String, Object> mergedOptions = mergeWithDefaultOptions(options);

        try (Playwright playwright = Playwright.create()) {
            Browser browser = createBrowser(playwright, mergedOptions);
            BrowserContext context = createBrowserContext(browser, mergedOptions);

            try {
                Page page = context.newPage();
                configurePage(page, mergedOptions);

                // Navigate to login page
                navigateToUrl(page, loginUrl, mergedOptions);

                // Perform login
                boolean loginSuccess = performLogin(page, loginCredentials, mergedOptions);
                if (!loginSuccess) {
                    log.error("Login failed");
                    return Map.of("error", "Login failed");
                }

                log.info("Login successful, navigating to target URL: {}", targetUrl);

                // Navigate to target URL
                navigateToUrl(page, targetUrl, mergedOptions);

                // Extract content using selectors
                Map<String, String> results = new HashMap<>();
                for (Map.Entry<String, String> entry : selectors.entrySet()) {
                    String label = entry.getKey();
                    String selector = entry.getValue();

                    try {
                        Locator locator = page.locator(selector);
                        if (locator.count() > 0) {
                            String content = extractContent(locator, mergedOptions);
                            results.put(label, content);
                        } else {
                            results.put(label, "");
                            log.warn("Selector not found: {}", selector);
                        }
                    } catch (Exception e) {
                        log.error("Error extracting content for selector {}: {}", selector, e.getMessage());
                        results.put(label, "ERROR: " + e.getMessage());
                    }
                }

                return results;
            } finally {
                context.close();
            }
        } catch (Exception e) {
            log.error("Error scraping with login: {}", e.getMessage());
            return Map.of("error", e.getMessage());
        }
    }

    @Override
    public Map<String, String> scrapeWithJavaScript(String url, Map<String, String> selectors, Map<String, Object> options) {
        // Enable JavaScript rendering in options
        Map<String, Object> jsOptions = new HashMap<>(options != null ? options : new HashMap<>());
        jsOptions.put("javaScriptEnabled", true);
        jsOptions.put("waitForSelector", jsOptions.getOrDefault("waitForSelector", "body"));
        jsOptions.put("waitForTimeout", jsOptions.getOrDefault("waitForTimeout", 5000));

        return scrapeWithSelectors(url, selectors, jsOptions);
    }

    @Override
    public List<Map<String, String>> scrapeMultipleUrls(List<String> urls, Map<String, String> selectors, Map<String, Object> options) {
        Map<String, Object> mergedOptions = mergeWithDefaultOptions(options);
        int maxThreads = Math.min(parallelRequests, urls.size());

        ExecutorService executor = Executors.newFixedThreadPool(maxThreads);
        List<Future<Map<String, String>>> futures = new ArrayList<>();

        // Submit scraping tasks
        for (String url : urls) {
            futures.add(executor.submit(() -> {
                Map<String, String> result = scrapeWithSelectors(url, selectors, mergedOptions);
                result.put("url", url);
                return result;
            }));
        }

        // Collect results
        List<Map<String, String>> results = new ArrayList<>();
        for (Future<Map<String, String>> future : futures) {
            try {
                results.add(future.get());
            } catch (Exception e) {
                log.error("Error getting result from parallel scraping: {}", e.getMessage());
                results.add(Map.of("error", e.getMessage()));
            }
        }

        executor.shutdown();
        try {
            if (!executor.awaitTermination(timeoutMs * urls.size(), TimeUnit.MILLISECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }

        return results;
    }

    // Helper methods

    /**
     * Merge provided options with default options
     */
    private Map<String, Object> mergeWithDefaultOptions(Map<String, Object> options) {
        Map<String, Object> defaultOptions = new HashMap<>();
        defaultOptions.put("headless", true);
        defaultOptions.put("timeout", timeoutMs);
        defaultOptions.put("javaScriptEnabled", true);
        defaultOptions.put("isMobile", false);
        defaultOptions.put("extractAttributes", false);
        defaultOptions.put("extractHtml", false);
        defaultOptions.put("waitUntil", "networkidle");
        defaultOptions.put("retries", maxRetries);
        defaultOptions.put("retryDelay", retryDelayMs);
        defaultOptions.put("useRandomDelays", useRandomDelays);
        defaultOptions.put("minDelay", minDelayMs);
        defaultOptions.put("maxDelay", maxDelayMs);
        defaultOptions.put("continueOnError", true);
        defaultOptions.put("antiDetection", antiDetectionEnabled);


        if (options != null) {
            defaultOptions.putAll(options);
        }

        return defaultOptions;
    }

    /**
     * Create a browser instance with the specified options
     */
    private Browser createBrowser(Playwright playwright, Map<String, Object> options) {
        boolean headless = (boolean) options.getOrDefault("headless", true);
        int timeout = (int) options.getOrDefault("timeout", timeoutMs);

        BrowserType.LaunchOptions launchOptions = new BrowserType.LaunchOptions()
                .setHeadless(headless)
                .setTimeout(timeout);

        return playwright.chromium().launch(launchOptions);
    }

    private BrowserContext createBrowserContext(Browser browser, Map<String, Object> options) {
        boolean isMobile = (boolean) options.getOrDefault("isMobile", false);
        String userAgent = userAgentRotator.getRandomUserAgent(isMobile);
        boolean antiDetection = (boolean) options.get("antiDetection");

        int[] viewport = headerGenerator.generateRandomViewport(isMobile);

        Browser.NewContextOptions contextOptions = new Browser.NewContextOptions()
                .setUserAgent(userAgent)
                .setViewportSize(viewport[0], viewport[1]);

        if (antiDetection) {
            // Further reduce fingerprinting
            contextOptions.setJavaScriptEnabled(true);
            contextOptions.setBypassCSP(true);
        }


        boolean javaScriptEnabled = (boolean) options.getOrDefault("javaScriptEnabled", true);
        contextOptions.setJavaScriptEnabled(javaScriptEnabled);

        if (options.containsKey("latitude") && options.containsKey("longitude")) {
            double latitude = Double.parseDouble(options.get("latitude").toString());
            double longitude = Double.parseDouble(options.get("longitude").toString());
            contextOptions.setGeolocation(latitude, longitude);
        }

        if (options.containsKey("locale")) {
            contextOptions.setLocale((String) options.get("locale"));
        }

        if (options.containsKey("timezone")) {
            contextOptions.setTimezoneId((String) options.get("timezone"));
        }

        return browser.newContext(contextOptions);
    }

    private void configurePage(Page page, Map<String, Object> options) {
        int timeout = (int) options.getOrDefault("timeout", timeoutMs);
        page.setDefaultTimeout(timeout);

        boolean isMobile = (boolean) options.getOrDefault("isMobile", false);
        String referrer = (String) options.getOrDefault("referrer", "");
        headerGenerator.applyHeadersToPage(page, isMobile, referrer);
    }

    private void navigateToUrl(Page page, String url, Map<String, Object> options) {
        int retries = (int) options.getOrDefault("retries", maxRetries);
        String waitUntil = (String) options.getOrDefault("waitUntil", "networkidle");
        boolean antiDetection = (boolean) options.get("antiDetection");


        WaitUntilState waitUntilState;
        switch (waitUntil.toLowerCase()) {
            case "load":
                waitUntilState = WaitUntilState.LOAD;
                break;
            case "domcontentloaded":
                waitUntilState = WaitUntilState.DOMCONTENTLOADED;
                break;
            case "networkidle":
            default:
                waitUntilState = WaitUntilState.NETWORKIDLE;
                break;
        }

        Exception lastException = null;
        for (int attempt = 0; attempt < retries; attempt++) {
            try {
                log.info("Navigating to URL: {} (attempt {}/{})", url, attempt + 1, retries);
                page.navigate(url, new Page.NavigateOptions().setWaitUntil(waitUntilState));

                if (antiDetection) {
                    // Add a random delay after navigation to appear more human
                    int delay = minDelayMs + random.nextInt(maxDelayMs - minDelayMs);
                    log.debug("Waiting {} ms after navigation for anti-detection.", delay);
                    page.waitForTimeout(delay);
                }


                String waitForSelector = (String) options.getOrDefault("waitForSelector", "");
                if (!waitForSelector.isEmpty()) {
                    page.waitForSelector(waitForSelector);
                }

                Integer waitForTimeout = (Integer) options.getOrDefault("waitForTimeout", 0);
                if (waitForTimeout > 0) {
                    page.waitForTimeout(waitForTimeout);
                }

                return; // Success
            } catch (Exception e) {
                lastException = e;
                log.warn("Navigation attempt {} failed: {}", attempt + 1, e.getMessage());

                if (attempt < retries - 1) {
                    try {
                        // Use exponential backoff with jitter for retries
                        int retryDelay = (int) (retryDelayMs * Math.pow(2, attempt)) + random.nextInt(1000);
                        log.info("Retrying in {} ms", retryDelay);
                        Thread.sleep(retryDelay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Navigation interrupted", ie);
                    }
                }
            }
        }

        throw new RuntimeException("Failed to navigate to URL after " + retries + " attempts", lastException);
    }

    private String extractContent(Locator locator, Map<String, Object> options) {
        boolean extractHtml = (boolean) options.getOrDefault("extractHtml", false);
        boolean extractAttributes = (boolean) options.getOrDefault("extractAttributes", false);
        String attributeName = (String) options.getOrDefault("attributeName", "");

        if (extractHtml) {
            return locator.innerHTML();
        } else if (extractAttributes && !attributeName.isEmpty()) {
            return locator.getAttribute(attributeName);
        } else {
            return locator.innerText();
        }
    }

    private boolean performLogin(Page page, Map<String, String> loginCredentials, Map<String, Object> options) {
        // This method remains largely the same, but the human-like typing delays are even more important now.
        try {
            String usernameSelector = loginCredentials.getOrDefault("usernameSelector", "input[type='email'], input[type='text'], input[name='username'], input[name='email']");
            String passwordSelector = loginCredentials.getOrDefault("passwordSelector", "input[type='password'], input[name='password']");
            String submitSelector = loginCredentials.getOrDefault("submitSelector", "button[type='submit'], input[type='submit']");
            String username = loginCredentials.getOrDefault("username", "");
            String password = loginCredentials.getOrDefault("password", "");

            if (username.isEmpty() || password.isEmpty()) {
                log.error("Username or password not provided");
                return false;
            }

            page.waitForSelector(usernameSelector);
            page.fill(usernameSelector, "");
            for (char c : username.toCharArray()) {
                String currentText = page.inputValue(usernameSelector);
                page.fill(usernameSelector, currentText + c);
                page.waitForTimeout(70 + random.nextInt(120)); // Slower, more variable typing
            }

            page.waitForTimeout(600 + random.nextInt(1100));

            page.fill(passwordSelector, "");
            for (char c : password.toCharArray()) {
                String currentText = page.inputValue(passwordSelector);
                page.fill(passwordSelector, currentText + c);
                page.waitForTimeout(80 + random.nextInt(130));
            }

            page.waitForTimeout(700 + random.nextInt(1200));
            page.click(submitSelector);
            page.waitForLoadState(LoadState.NETWORKIDLE);

            String loginSuccessSelector = loginCredentials.getOrDefault("loginSuccessSelector", "");
            String loginErrorSelector = loginCredentials.getOrDefault("loginErrorSelector", "");

            if (!loginSuccessSelector.isEmpty() && page.locator(loginSuccessSelector).count() > 0) {
                log.info("Login success confirmed by success selector");
                return true;
            }

            if (!loginErrorSelector.isEmpty() && page.locator(loginErrorSelector).count() > 0) {
                log.error("Login failed - error selector found");
                return false;
            }

            log.info("Login assumed successful (no specific success/error selectors provided)");
            return true;
        } catch (Exception e) {
            log.error("Error during login: {}", e.getMessage());
            return false;
        }
    }
}
