package com.enosisbd.app.flowable;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import com.enosisbd.app.entity.ScrapeEntity;
import com.enosisbd.app.repository.ScrapeRepository;
import com.enosisbd.app.service.ScrapeSchedulerService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("databaseStorageDelegate")
@RequiredArgsConstructor
public class DatabaseStorageDelegate implements JavaDelegate {

    private final ScrapeRepository scrapeRepository;
    private final ScrapeSchedulerService scrapeSchedulerService;

    @Override
    public void execute(DelegateExecution execution) {
        Long scrapeEntityId = (Long) execution.getVariable("scrapeEntityId");
        
        log.info("Starting database storage for scrape entity: {}", scrapeEntityId);
        
        try {
            // Get the scrape entity
            ScrapeEntity entity = scrapeRepository.findById(scrapeEntityId)
                .orElseThrow(() -> new RuntimeException("Scrape entity not found: " + scrapeEntityId));
            
            // Get extracted data from workflow variables
            String extractedData = (String) execution.getVariable("extractedData");
            Long extractionTimestamp = (Long) execution.getVariable("extractionTimestamp");
            
            // TODO: Store the extracted data to database
            // This is where you would save the actual scraped data
            // For now, we'll just log the completion
            
            log.info("Storing extracted data for entity {}: {}", scrapeEntityId, extractedData);
            
            // Simulate database storage
            Thread.sleep(1000); // Simulate 1 second of database operations
            
            // Mark the scrape entity as completed
            scrapeSchedulerService.markAsCompleted(entity);
            
            log.info("Database storage completed successfully for entity: {}", scrapeEntityId);
            
        } catch (Exception e) {
            log.error("Database storage failed for entity {}: {}", scrapeEntityId, e.getMessage(), e);
            
            // Mark the entity as failed
            scrapeRepository.findById(scrapeEntityId).ifPresent(entity -> 
                scrapeSchedulerService.markAsFailed(entity, "Database storage failed: " + e.getMessage())
            );
            
            throw new RuntimeException("Database storage failed", e);
        }
    }
}
