package com.enosisbd.app.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

// --- Achievement ---
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Achievement {
    private UUID achievementId;
    private UUID companyId; // Foreign Key to Company

    // For Many-to-One relationship with Company
    // In a real JPA/Hibernate setup, this would often be mapped with @ManyToOne
    private WebsiteInfo company;

    private String type;
    private String name;
    private String description;
    private String sourceURL;
    private Integer year;
}
