package com.enosisbd.app.gemini;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Jacksonized
@Builder
public class GeminiRequest {
    @JsonProperty("contents")
    private List<Content> contents;

    @Data
    @Jacksonized
    @Builder
    public static class Content {
        @JsonProperty("parts")
        private List<Part> parts;
    }

    @Data
    @Jacksonized
    @Builder
    public static class Part {
        @JsonProperty("text")
        private String text;
    }
}
