package com.enosisbd.app.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

// --- ProductService ---
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductService {
    private UUID productServiceId;
    private UUID companyId; // Foreign Key to Company

    // For Many-to-One relationship with Company
    // In a real JPA/Hibernate setup, this would often be mapped with @ManyToOne
    private WebsiteInfo company;

    private String name;
    private String type;
    private String description;

    // For Many-to-Many relationship with Platform (managed through ProductServicePlatform table)
    // In a real JPA/Hibernate setup, this would often be mapped with @ManyToMany
    private List<Platform> platforms = new ArrayList<>();

    // For Many-to-Many relationship with Feature (managed through ProductServiceFeature table)
    // In a real JPA/Hibernate setup, this would often be mapped with @ManyToMany
    private List<Feature> features = new ArrayList<>();
}
