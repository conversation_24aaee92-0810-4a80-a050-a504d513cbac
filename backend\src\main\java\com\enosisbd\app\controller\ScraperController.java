package com.enosisbd.app.controller;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.enosisbd.app.controller.request.ScrapeRequest;
import com.enosisbd.app.entity.CompanyEntity;
import com.enosisbd.app.entity.ScrapeEntity;
import com.enosisbd.app.entity.ScrapeStatus;
import com.enosisbd.app.service.ScraperService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.opencsv.CSVReader;
import com.opencsv.CSVWriter;
import com.opencsv.exceptions.CsvValidationException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/scrapping")
public class ScraperController implements ScraperApi {

    private final ScraperService scraperService;
    private final ObjectMapper objectMapper;

    @Override
    @PostMapping
    public ResponseEntity<?> scrape(@RequestBody ScrapeRequest scrapeRequest) {
        scraperService.saveScrapeRequest(scrapeRequest);
        return ResponseEntity.accepted().body(Map.of("message","Scrape Request Started!!"));
    }

    @GetMapping("/convert-json-to-csv")
    public ResponseEntity<byte[]> convertJsonToCsv() {
        try {
            // Read the JSON file
            InputStream inputStream = getClass().getResourceAsStream("/enosis.json");
            List<ScrapeRequest> requests = objectMapper.readValue(
                inputStream,
                new TypeReference<List<ScrapeRequest>>() {}
            );

            // Convert to CSV
            StringWriter stringWriter = new StringWriter();
            CSVWriter csvWriter = new CSVWriter(stringWriter);

            // Write header
            csvWriter.writeNext(new String[]{
                "name", "glassdoor", "clutch", "goodfirms", "linkedin", "website"
            });

            // Write data
            for (ScrapeRequest request : requests) {
                csvWriter.writeNext(new String[]{
                    request.getName(),
                    request.getGlassdoor(),
                    request.getClutch(),
                    request.getGoodfirms(),
                    request.getLinkedin(),
                    request.getWebsite()
                });
            }

            csvWriter.close();

            // Set up response headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("text/csv"));
            headers.setContentDispositionFormData("attachment", "companies.csv");

            return ResponseEntity
                .ok()
                .headers(headers)
                .body(stringWriter.toString().getBytes());

        } catch (Exception e) {
            log.error("Error converting JSON to CSV", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @PostMapping("/upload")
    public ResponseEntity<?> uploadCsv(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty() || !file.getOriginalFilename().endsWith(".csv")) {
            return ResponseEntity.badRequest().body(Map.of("error", "Please upload a valid CSV file"));
        }

        List<String> errors = new ArrayList<>();
        int successCount = 0;
        String csvUploadId = UUID.randomUUID().toString();
        String csvFileName = file.getOriginalFilename();

        try (CSVReader reader = new CSVReader(new InputStreamReader(file.getInputStream()))) {
            String[] header = reader.readNext(); // Read header
            if (header == null || !isValidHeader(header)) {
                return ResponseEntity.badRequest().body(Map.of("error", "Invalid CSV format. Required columns: name, glassdoor, clutch, goodfirms, linkedin, website"));
            }

            Map<String, Integer> headerMap = createHeaderMap(header);
            String[] line;
            while ((line = reader.readNext()) != null) {
                try {
                    ScrapeRequest request = createScrapeRequest(line, headerMap);
                    request.setCsvFileName(csvFileName);
                    request.setCsvUploadId(csvUploadId);
                    scraperService.saveScrapeRequest(request);
                    successCount++;
                } catch (Exception e) {
                    errors.add("Error processing line: " + String.join(",", line) + " - " + e.getMessage());
                }
            }
        } catch (IOException | CsvValidationException e) {
            log.error("Error processing CSV file", e);
            return ResponseEntity.internalServerError().body(Map.of("error", "Error processing CSV file: " + e.getMessage()));
        }

        Map<String, Object> response = new HashMap<>();
        response.put("successCount", successCount);
        response.put("csvUploadId", csvUploadId);
        response.put("csvFileName", csvFileName);
        if (!errors.isEmpty()) {
            response.put("errors", errors);
        }
        return ResponseEntity.ok(response);
    }

    @GetMapping("/csv-uploads")
    public ResponseEntity<List<Map<String, Object>>> getCsvUploads() {
        List<Map<String, Object>> uploads = scraperService.getCsvUploads();
        return ResponseEntity.ok(uploads);
    }

    @GetMapping("/download/{csvUploadId}")
    public ResponseEntity<byte[]> downloadScrapedCompanies(@PathVariable String csvUploadId) {
        try {
            List<ScrapeEntity> scrapes = scraperService.getScrapesByCsvUploadId(csvUploadId);
            if (scrapes.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            StringWriter stringWriter = new StringWriter();
            CSVWriter csvWriter = new CSVWriter(stringWriter);

            // Write header
            csvWriter.writeNext(new String[]{
                // Basic Info
                "Company Name",
                "Last Scraped",
                "Data Completeness Score",
                "Created At",
                "Updated At",
                
                // Clutch Data
                "Clutch Status",
                "Overall Rating",
                "Rating",
                "Reviews Count",
                "Location",
                "Company Size",
                "Founded Year",
                "Hourly Rate Range",
                "Min Project Size",
                "Company URL",
                "Company Logo URL",
                "Company Overview",
                "Company Description",
                "Services",
                "Industries",
                "Specialties",
                "Provider Name",
                "Profile URL",
                "Verification Badge",
                "Verification Status",
                "Verification Text",
                "Quality Rating",
                "Schedule Rating",
                "Cost Rating",
                "Willing to Refer",
                "Top Mentions",
                "City",
                "Country",
                
                // Goodfirms Data
                "Goodfirms Status",
                "Provider Name",
                "Profile URL",
                "Rating",
                "Reviews Count",
                "Location",
                "Verification Badge",
                "Client Likes",
                "Client Dislikes",
                
                // Glassdoor Data
                "Glassdoor Status",
                "Rating",
                "Reviews Count",
                "Employee Satisfaction",
                "Category Ratings",
                "Ratings Distribution",
                "Pros",
                "Cons",
                
                // LinkedIn Data
                "LinkedIn Status",
                "Followers",
                "Employees Count",
                "Industry",
                
                // Website Data
                "Website Status",
                "Title",
                "Description",
                "Keywords",
                "Name",
                "URL",
                "Founded Year",
                "Employee Size",
                "Headquarters Location",
                "City",
                "Country",
                "Focus Statement",
                "Culture Description",
                "Outcome Summary",
                "Industries",
                "Services",
                "Service Details",
                "Achievements",
                "Founding Story",
                "Global Presence",
                "Initiatives",
                "Aliases",
                "Funding Status"
            });

            // Write data
            for (ScrapeEntity scrape : scrapes) {
                CompanyEntity company = scrape.getCompanyEntity();
                if (company == null) continue;
                
                List<String> row = new ArrayList<>();

                // Basic Info
                row.add(scrape.getName());
                row.add(company.getLastScraped() != null ? company.getLastScraped().toString() : "");
                row.add(company.getDataCompletenessScore() != null ? String.format("%.2f%%", company.getDataCompletenessScore() * 100) : "");
                row.add(company.getCreatedAt() != null ? company.getCreatedAt().toString() : "");
                row.add(company.getUpdatedAt() != null ? company.getUpdatedAt().toString() : "");

                // Clutch Data
                row.add(String.valueOf(company.getClutchStatus()));
                row.add(company.getClutchOverallRating());
                row.add(company.getClutchRating() != null ? company.getClutchRating().toString() : "");
                row.add(company.getClutchTotalReviews() != null ? company.getClutchTotalReviews().toString() : "");
                row.add(String.format("%s, %s", company.getClutchCity(), company.getClutchCountry()).replaceAll("^,\\s*|\\s*,\\s*$", ""));
                row.add(company.getClutchCompanySize());
                row.add(company.getClutchFoundedYear());
                row.add(company.getClutchHourlyRateRange());
                row.add(company.getClutchMinimumProjectSize() != null ? company.getClutchMinimumProjectSize().toString() : "");
                row.add(company.getClutchCompanyUrl());
                row.add(company.getClutchCompanyLogoURL());
                row.add(company.getClutchCompanyOverview());
                row.add(company.getClutchCompanyDescription());
                row.add(company.getClutchAllServices());
                row.add(company.getClutchAllIndustries());
                row.add(company.getClutchSpecialties());
                row.add(company.getClutchProviderName());
                row.add(company.getClutchProfileUrl());
                row.add(company.getClutchVerificationBadge() != null ? company.getClutchVerificationBadge().toString() : "");
                row.add(company.getClutchVerified() != null ? company.getClutchVerified().toString() : "");
                row.add(company.getClutchVerificationBadgeText());
                row.add(company.getClutchQualityRating());
                row.add(company.getClutchScheduleRating());
                row.add(company.getClutchCostRating());
                row.add(company.getClutchWillingToRefer() != null ? company.getClutchWillingToRefer().toString() : "");
                row.add(company.getTopMentions());
                row.add(company.getClutchCity());
                row.add(company.getClutchCountry());

                // Goodfirms Data
                row.add(String.valueOf(company.getGoodfirmsStatus()));
                row.add(company.getGoodfirmsProviderName());
                row.add(company.getGoodfirmsProfileUrl());
                row.add(company.getGoodfirmsRating() != null ? company.getGoodfirmsRating().toString() : "");
                row.add(company.getGoodfirmsReviewsCount() != null ? company.getGoodfirmsReviewsCount().toString() : "");
                row.add(company.getGoodfirmsLocation());
                row.add(company.getGoodfirmsVerificationBadge() != null ? company.getGoodfirmsVerificationBadge().toString() : "");
                row.add(company.getGoodfirmsClientLikes());
                row.add(company.getGoodfirmsClientDislikes());

                // Glassdoor Data
                row.add(String.valueOf(company.getGlassdoorStatus()));
                row.add(company.getGlassdoorRating() != null ? company.getGlassdoorRating().toString() : "");
                row.add(company.getGlassdoorReviewsCount() != null ? company.getGlassdoorReviewsCount().toString() : "");
                row.add(company.getGlassdoorEmployeeSatisfaction() != null ? company.getGlassdoorEmployeeSatisfaction().toString() : "");
                row.add(company.getGlassdoorCategoryRatings());
                row.add(company.getGlassdoorRatingsDistribution());
                row.add(company.getGlassdoorPros());
                row.add(company.getGlassdoorCons());

                // LinkedIn Data
                row.add(String.valueOf(company.getLinkedinStatus()));
                row.add(company.getLinkedinFollowers() != null ? company.getLinkedinFollowers().toString() : "");
                row.add(company.getLinkedinEmployeesCount() != null ? company.getLinkedinEmployeesCount().toString() : "");
                row.add(company.getLinkedinIndustry());

                // Website Data
                row.add(String.valueOf(company.getWebsiteStatus()));
                row.add(company.getWebsiteTitle());
                row.add(company.getWebsiteDescription());
                row.add(company.getWebsiteKeywords());
                row.add(company.getWebsiteName());
                row.add(company.getWebsiteUrl());
                row.add(company.getWebsiteFoundedYear());
                row.add(company.getWebsiteEmployeeSize());
                row.add(company.getWebsiteHeadquartersLocation());
                row.add(company.getWebsiteCity());
                row.add(company.getWebsiteCountry());
                row.add(company.getWebsiteFocusStatement());
                row.add(company.getWebsiteCultureDescription());
                row.add(company.getWebsiteOutcomeSummary());
                row.add(company.getWebsiteIndustries());
                row.add(company.getWebsiteServices());
                row.add(company.getWebsiteServiceDetails());
                row.add(company.getWebsiteAchievements());
                row.add(company.getWebsiteFoundingStory());
                row.add(company.getWebsiteGlobalPresence());
                row.add(company.getWebsiteInitiatives());
                row.add(company.getWebsiteAliases());
                row.add(company.getWebsiteFundingStatus());

                csvWriter.writeNext(row.toArray(new String[0]));
            }

            csvWriter.close();

            // Set up response headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("text/csv"));
            String filename = String.format("scraped-companies-%s.csv", csvUploadId);
            headers.setContentDispositionFormData("attachment", filename);

            return ResponseEntity
                .ok()
                .headers(headers)
                .body(stringWriter.toString().getBytes());

        } catch (Exception e) {
            log.error("Error generating CSV for csvUploadId: " + csvUploadId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/download-all")
    public ResponseEntity<byte[]> downloadAllCompanies() {
        try {
            List<CompanyEntity> companies = scraperService.getAllCompanies();
            if (companies.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            StringWriter stringWriter = new StringWriter();
            CSVWriter csvWriter = new CSVWriter(stringWriter);

            // Write header (reuse the same headers as downloadScrapedCompanies)
            csvWriter.writeNext(new String[]{
                // Basic Info
                "Company Name",
                "Last Scraped",
                "Data Completeness Score",
                "Created At",
                "Updated At",
                
                // Clutch Data
                "Clutch Status",
                "Overall Rating",
                "Rating",
                "Reviews Count",
                "Location",
                "Company Size",
                "Founded Year",
                "Hourly Rate Range",
                "Min Project Size",
                "Company URL",
                "Company Logo URL",
                "Company Overview",
                "Company Description",
                "Services",
                "Industries",
                "Specialties",
                "Provider Name",
                "Profile URL",
                "Verification Badge",
                "Verification Status",
                "Verification Text",
                "Quality Rating",
                "Schedule Rating",
                "Cost Rating",
                "Willing to Refer",
                "Top Mentions",
                "City",
                "Country",
                
                // Goodfirms Data
                "Goodfirms Status",
                "Provider Name",
                "Profile URL",
                "Rating",
                "Reviews Count",
                "Location",
                "Verification Badge",
                "Client Likes",
                "Client Dislikes",
                
                // Glassdoor Data
                "Glassdoor Status",
                "Rating",
                "Reviews Count",
                "Employee Satisfaction",
                "Category Ratings",
                "Ratings Distribution",
                "Pros",
                "Cons",
                
                // LinkedIn Data
                "LinkedIn Status",
                "Followers",
                "Employees Count",
                "Industry",
                
                // Website Data
                "Website Status",
                "Title",
                "Description",
                "Keywords",
                "Name",
                "URL",
                "Founded Year",
                "Employee Size",
                "Headquarters Location",
                "City",
                "Country",
                "Focus Statement",
                "Culture Description",
                "Outcome Summary",
                "Industries",
                "Services",
                "Service Details",
                "Achievements",
                "Founding Story",
                "Global Presence",
                "Initiatives",
                "Aliases",
                "Funding Status"
            });

            // Write data
            for (CompanyEntity company : companies) {
                List<String> row = new ArrayList<>();

                // Basic Info
                row.add(company.getName());
                row.add(company.getLastScraped() != null ? company.getLastScraped().toString() : "");
                row.add(company.getDataCompletenessScore() != null ? String.format("%.2f%%", company.getDataCompletenessScore() * 100) : "");
                row.add(company.getCreatedAt() != null ? company.getCreatedAt().toString() : "");
                row.add(company.getUpdatedAt() != null ? company.getUpdatedAt().toString() : "");

                // Clutch Data
                row.add(String.valueOf(company.getClutchStatus()));
                row.add(company.getClutchOverallRating());
                row.add(company.getClutchRating() != null ? company.getClutchRating().toString() : "");
                row.add(company.getClutchTotalReviews() != null ? company.getClutchTotalReviews().toString() : "");
                row.add(String.format("%s, %s", company.getClutchCity(), company.getClutchCountry()).replaceAll("^,\\s*|\\s*,\\s*$", ""));
                row.add(company.getClutchCompanySize());
                row.add(company.getClutchFoundedYear());
                row.add(company.getClutchHourlyRateRange());
                row.add(company.getClutchMinimumProjectSize() != null ? company.getClutchMinimumProjectSize().toString() : "");
                row.add(company.getClutchCompanyUrl());
                row.add(company.getClutchCompanyLogoURL());
                row.add(company.getClutchCompanyOverview());
                row.add(company.getClutchCompanyDescription());
                row.add(company.getClutchAllServices());
                row.add(company.getClutchAllIndustries());
                row.add(company.getClutchSpecialties());
                row.add(company.getClutchProviderName());
                row.add(company.getClutchProfileUrl());
                row.add(company.getClutchVerificationBadge() != null ? company.getClutchVerificationBadge().toString() : "");
                row.add(company.getClutchVerified() != null ? company.getClutchVerified().toString() : "");
                row.add(company.getClutchVerificationBadgeText());
                row.add(company.getClutchQualityRating());
                row.add(company.getClutchScheduleRating());
                row.add(company.getClutchCostRating());
                row.add(company.getClutchWillingToRefer() != null ? company.getClutchWillingToRefer().toString() : "");
                row.add(company.getTopMentions());
                row.add(company.getClutchCity());
                row.add(company.getClutchCountry());

                // Goodfirms Data
                row.add(String.valueOf(company.getGoodfirmsStatus()));
                row.add(company.getGoodfirmsProviderName());
                row.add(company.getGoodfirmsProfileUrl());
                row.add(company.getGoodfirmsRating() != null ? company.getGoodfirmsRating().toString() : "");
                row.add(company.getGoodfirmsReviewsCount() != null ? company.getGoodfirmsReviewsCount().toString() : "");
                row.add(company.getGoodfirmsLocation());
                row.add(company.getGoodfirmsVerificationBadge() != null ? company.getGoodfirmsVerificationBadge().toString() : "");
                row.add(company.getGoodfirmsClientLikes());
                row.add(company.getGoodfirmsClientDislikes());

                // Glassdoor Data
                row.add(String.valueOf(company.getGlassdoorStatus()));
                row.add(company.getGlassdoorRating() != null ? company.getGlassdoorRating().toString() : "");
                row.add(company.getGlassdoorReviewsCount() != null ? company.getGlassdoorReviewsCount().toString() : "");
                row.add(company.getGlassdoorEmployeeSatisfaction() != null ? company.getGlassdoorEmployeeSatisfaction().toString() : "");
                row.add(company.getGlassdoorCategoryRatings());
                row.add(company.getGlassdoorRatingsDistribution());
                row.add(company.getGlassdoorPros());
                row.add(company.getGlassdoorCons());

                // LinkedIn Data
                row.add(String.valueOf(company.getLinkedinStatus()));
                row.add(company.getLinkedinFollowers() != null ? company.getLinkedinFollowers().toString() : "");
                row.add(company.getLinkedinEmployeesCount() != null ? company.getLinkedinEmployeesCount().toString() : "");
                row.add(company.getLinkedinIndustry());

                // Website Data
                row.add(String.valueOf(company.getWebsiteStatus()));
                row.add(company.getWebsiteTitle());
                row.add(company.getWebsiteDescription());
                row.add(company.getWebsiteKeywords());
                row.add(company.getWebsiteName());
                row.add(company.getWebsiteUrl());
                row.add(company.getWebsiteFoundedYear());
                row.add(company.getWebsiteEmployeeSize());
                row.add(company.getWebsiteHeadquartersLocation());
                row.add(company.getWebsiteCity());
                row.add(company.getWebsiteCountry());
                row.add(company.getWebsiteFocusStatement());
                row.add(company.getWebsiteCultureDescription());
                row.add(company.getWebsiteOutcomeSummary());
                row.add(company.getWebsiteIndustries());
                row.add(company.getWebsiteServices());
                row.add(company.getWebsiteServiceDetails());
                row.add(company.getWebsiteAchievements());
                row.add(company.getWebsiteFoundingStory());
                row.add(company.getWebsiteGlobalPresence());
                row.add(company.getWebsiteInitiatives());
                row.add(company.getWebsiteAliases());
                row.add(company.getWebsiteFundingStatus());

                csvWriter.writeNext(row.toArray(new String[0]));
            }

            csvWriter.close();

            // Set up response headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("text/csv"));
            String timestamp = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String filename = String.format("all-companies-%s.csv", timestamp);
            headers.setContentDispositionFormData("attachment", filename);

            return ResponseEntity
                .ok()
                .headers(headers)
                .body(stringWriter.toString().getBytes());

        } catch (Exception e) {
            log.error("Error generating CSV for all companies", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    private boolean isValidHeader(String[] header) {
        List<String> requiredColumns = List.of("name", "glassdoor", "clutch", "goodfirms", "linkedin", "website");
        List<String> headerList = List.of(header);
        return headerList.containsAll(requiredColumns);
    }

    private Map<String, Integer> createHeaderMap(String[] header) {
        Map<String, Integer> headerMap = new HashMap<>();
        for (int i = 0; i < header.length; i++) {
            headerMap.put(header[i].toLowerCase().trim(), i);
        }
        return headerMap;
    }

    private ScrapeRequest createScrapeRequest(String[] line, Map<String, Integer> headerMap) {
        ScrapeRequest request = new ScrapeRequest();
        request.setName(getValue(line, headerMap, "name"));
        request.setGlassdoor(getValue(line, headerMap, "glassdoor"));
        request.setClutch(getValue(line, headerMap, "clutch"));
        request.setGoodfirms(getValue(line, headerMap, "goodfirms"));
        request.setLinkedin(getValue(line, headerMap, "linkedin"));
        request.setWebsite(getValue(line, headerMap, "website"));
        return request;
    }

    private String getValue(String[] line, Map<String, Integer> headerMap, String column) {
        Integer index = headerMap.get(column);
        if (index != null && index < line.length) {
            String value = line[index].trim();
            return value.isEmpty() ? null : value;
        }
        return null;
    }
    
    @Override
    @GetMapping("/queue")
    public ResponseEntity<List<ScrapeEntity>> getAllQueuedScrapes() {
        log.info("Fetching all queued scrape requests");
        List<ScrapeEntity> queuedScrapes = scraperService.getAllQueuedScrapes();
        return ResponseEntity.ok(queuedScrapes);
    }
    
    @Override
    @GetMapping("/scheduled")
    public ResponseEntity<List<ScrapeEntity>> getAllScheduledScrapes() {
        log.info("Fetching all scheduled scrape requests");
        List<ScrapeEntity> scheduledScrapes = scraperService.getAllScrapesByStatus(ScrapeStatus.SCHEDULED);
        return ResponseEntity.ok(scheduledScrapes);
    }
    
    @Override
    @GetMapping("/processing")
    public ResponseEntity<List<ScrapeEntity>> getAllProcessingScrapes() {
        log.info("Fetching all processing scrape requests");
        List<ScrapeEntity> processingScrapes = scraperService.getAllScrapesByStatus(ScrapeStatus.IN_PROGRESS);
        return ResponseEntity.ok(processingScrapes);
    }
    
    @Override
    @GetMapping("/completed")
    public ResponseEntity<List<ScrapeEntity>> getAllCompletedScrapes() {
        log.info("Fetching all completed scrape requests");
        List<ScrapeEntity> completedScrapes = scraperService.getAllScrapesByStatus(ScrapeStatus.SUCCESS);
        return ResponseEntity.ok(completedScrapes);
    }
    
    @Override
    @GetMapping("/failed")
    public ResponseEntity<List<ScrapeEntity>> getAllFailedScrapes() {
        log.info("Fetching all failed scrape requests");
        List<ScrapeEntity> failedScrapes = scraperService.getAllScrapesByStatus(ScrapeStatus.FAILED);
        return ResponseEntity.ok(failedScrapes);
    }
    
    /**
     * Debug endpoint to get system status overview
     */
    @GetMapping("/debug/status")
    public ResponseEntity<Map<String, Object>> getSystemStatus() {
        log.info("Fetching system status overview");
        
        Map<String, Object> status = new HashMap<>();
        
        // Scrape entity counts
        status.put("queue", scraperService.getAllScrapesByStatus(ScrapeStatus.QUEUE).size());
        status.put("scheduled", scraperService.getAllScrapesByStatus(ScrapeStatus.SCHEDULED).size());
        status.put("processing", scraperService.getAllScrapesByStatus(ScrapeStatus.IN_PROGRESS).size());
        status.put("completed", scraperService.getAllScrapesByStatus(ScrapeStatus.SUCCESS).size());
        status.put("failed", scraperService.getAllScrapesByStatus(ScrapeStatus.FAILED).size());
        
        // Company statistics
        List<CompanyEntity> allCompanies = scraperService.getAllCompanies();
        status.put("totalCompanies", allCompanies.size());
        
        long clutchScraped = allCompanies.stream().filter(c -> Boolean.TRUE.equals(c.getClutchStatus())).count();
        long goodfirmsScraped = allCompanies.stream().filter(c -> Boolean.TRUE.equals(c.getGoodfirmsStatus())).count();
        long glassdoorScraped = allCompanies.stream().filter(c -> Boolean.TRUE.equals(c.getGlassdoorStatus())).count();
        long linkedinScraped = allCompanies.stream().filter(c -> Boolean.TRUE.equals(c.getLinkedinStatus())).count();
        long websiteScraped = allCompanies.stream().filter(c -> Boolean.TRUE.equals(c.getWebsiteStatus())).count();
        
        Map<String, Object> companyStats = new HashMap<>();
        companyStats.put("clutchScraped", clutchScraped);
        companyStats.put("goodfirmsScraped", goodfirmsScraped);
        companyStats.put("glassdoorScraped", glassdoorScraped);
        companyStats.put("linkedinScraped", linkedinScraped);
        companyStats.put("websiteScraped", websiteScraped);
        
        double avgCompleteness = allCompanies.stream()
                .filter(c -> c.getDataCompletenessScore() != null)
                .mapToDouble(CompanyEntity::getDataCompletenessScore)
                .average()
                .orElse(0.0);
        companyStats.put("averageCompleteness", Math.round(avgCompleteness * 100.0) / 100.0);
        
        status.put("companyStats", companyStats);
        
        // Add detailed info about queue items
        List<ScrapeEntity> queueItems = scraperService.getAllScrapesByStatus(ScrapeStatus.QUEUE);
        List<Map<String, Object>> queueDetails = queueItems.stream().map(item -> {
            Map<String, Object> detail = new HashMap<>();
            detail.put("id", item.getId());
            detail.put("name", item.getName());
            detail.put("nextScheduledTime", item.getNextScheduledTime());
            detail.put("retryCount", item.getRetryCount());
            detail.put("companyId", item.getCompanyEntity() != null ? item.getCompanyEntity().getId() : null);
            return detail;
        }).toList();
        status.put("queueDetails", queueDetails);
        
        // Add detailed info about scheduled items
        List<ScrapeEntity> scheduledItems = scraperService.getAllScrapesByStatus(ScrapeStatus.SCHEDULED);
        List<Map<String, Object>> scheduledDetails = scheduledItems.stream().map(item -> {
            Map<String, Object> detail = new HashMap<>();
            detail.put("id", item.getId());
            detail.put("name", item.getName());
            detail.put("scheduledTime", item.getNextScheduledTime());
            detail.put("minutesUntilReady", java.time.Duration.between(
                java.time.LocalDateTime.now(), item.getNextScheduledTime()).toMinutes());
            detail.put("companyId", item.getCompanyEntity() != null ? item.getCompanyEntity().getId() : null);
            return detail;
        }).toList();
        status.put("scheduledDetails", scheduledDetails);
        
        // Recent company activity
        List<CompanyEntity> recentlyScraped = allCompanies.stream()
                .filter(c -> c.getLastScraped() != null)
                .sorted((c1, c2) -> c2.getLastScraped().compareTo(c1.getLastScraped()))
                .limit(5)
                .toList();
        
        List<Map<String, Object>> recentActivity = recentlyScraped.stream().map(company -> {
            Map<String, Object> activity = new HashMap<>();
            activity.put("companyId", company.getId());
            activity.put("name", company.getName());
            activity.put("lastScraped", company.getLastScraped());
            activity.put("completenessScore", company.getDataCompletenessScore());
            return activity;
        }).toList();
        status.put("recentActivity", recentActivity);
        
        status.put("currentTime", java.time.LocalDateTime.now());
        
        return ResponseEntity.ok(status);
    }
    
    /**
     * Debug endpoint to create a test scrape request quickly
     */
    @PostMapping("/debug/test-request")
    public ResponseEntity<Map<String, String>> createTestRequest() {
        log.info("Creating test scrape request");
        
        ScrapeRequest testRequest = new ScrapeRequest();
        testRequest.setName("Test Company " + System.currentTimeMillis());
        testRequest.setGlassdoor("https://glassdoor.com/test");
        testRequest.setClutch("https://clutch.co/test");
        testRequest.setGoodfirms("https://goodfirms.co/test");
        testRequest.setWebsite("https://test.com");
        
        scraperService.saveScrapeRequest(testRequest);
        
        return ResponseEntity.ok(Map.of("message", "Test request created: " + testRequest.getName()));
    }
    
    /**
     * Get all companies with their scraped data
     */
    @GetMapping("/companies")
    public ResponseEntity<List<CompanyEntity>> getAllCompanies() {
        log.info("Fetching all companies");
        List<CompanyEntity> companies = scraperService.getAllCompanies();
        return ResponseEntity.ok(companies);
    }
    
    /**
     * Get company by ID
     */
    @GetMapping("/companies/{id}")
    public ResponseEntity<CompanyEntity> getCompanyById(@PathVariable Long id) {
        log.info("Fetching company with ID: {}", id);
        return scraperService.getCompanyById(id)
                .map(company -> ResponseEntity.ok(company))
                .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * Get companies by completeness score
     */
    @GetMapping("/companies/by-completeness")
    public ResponseEntity<List<CompanyEntity>> getCompaniesByCompleteness(
            @RequestParam(defaultValue = "50.0") Double minScore) {
        log.info("Fetching companies with completeness score >= {}", minScore);
        List<CompanyEntity> companies = scraperService.getCompaniesByCompleteness(minScore);
        return ResponseEntity.ok(companies);
    }
}
