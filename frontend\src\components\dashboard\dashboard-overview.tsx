import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Building2, 
  Webhook, 
  TrendingUp,
  CheckCircle,
  Clock,
  AlertCircle,
  Database
} from 'lucide-react'

interface MetricCardProps {
  title: string
  value: string | number
  description: string
  icon: React.ComponentType<{ className?: string }>
  trend?: {
    value: number
    isPositive: boolean
  }
}

function MetricCard({ title, value, description, icon: Icon, trend }: MetricCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <p className="text-xs text-muted-foreground">{description}</p>
        {trend && (
          <div className="flex items-center mt-2">
            <TrendingUp className={`h-3 w-3 mr-1 ${trend.isPositive ? 'text-green-500' : 'text-red-500'}`} />
            <span className={`text-xs ${trend.isPositive ? 'text-green-500' : 'text-red-500'}`}>
              {trend.isPositive ? '+' : ''}{trend.value}%
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

interface JobStatusProps {
  status: 'completed' | 'running' | 'failed' | 'pending'
  count: number
  percentage: number
}

function JobStatus({ status, count, percentage }: JobStatusProps) {
  const statusConfig = {
    completed: { icon: CheckCircle, color: 'text-green-500', bg: 'bg-green-100' },
    running: { icon: Clock, color: 'text-blue-500', bg: 'bg-blue-100' },
    failed: { icon: AlertCircle, color: 'text-red-500', bg: 'bg-red-100' },
    pending: { icon: Clock, color: 'text-yellow-500', bg: 'bg-yellow-100' }
  }

  const config = statusConfig[status]
  const Icon = config.icon

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-2">
        <Icon className={`h-4 w-4 ${config.color}`} />
        <span className="text-sm font-medium capitalize">{status}</span>
        <Badge variant="secondary">{count}</Badge>
      </div>
      <div className="flex items-center space-x-2">
        <Progress value={percentage} className="w-20" />
        <span className="text-xs text-muted-foreground">{percentage}%</span>
      </div>
    </div>
  )
}

export function DashboardOverview() {
  const metrics = [
    {
      title: 'Total Companies',
      value: '1,234',
      description: 'Companies in database',
      icon: Building2,
      trend: { value: 12, isPositive: true }
    },
    {
      title: 'Active Jobs',
      value: '23',
      description: 'Currently running',
      icon: Webhook,
      trend: { value: 5, isPositive: true }
    },
    {
      title: 'Data Points',
      value: '89.2K',
      description: 'Total scraped data',
      icon: Database,
      trend: { value: 15, isPositive: true }
    }
  ]

  const jobStatuses = [
    { status: 'completed' as const, count: 156, percentage: 78 },
    { status: 'running' as const, count: 23, percentage: 12 },
    { status: 'failed' as const, count: 12, percentage: 6 },
    { status: 'pending' as const, count: 8, percentage: 4 }
  ]

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
        <p className="text-muted-foreground">
          Welcome back! Here's an overview of your scraping operations.
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {metrics.map((metric) => (
          <MetricCard key={metric.title} {...metric} />
        ))}
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Job Status Overview</CardTitle>
            <CardDescription>
              Current status of all scraping jobs
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {jobStatuses.map((jobStatus) => (
              <JobStatus key={jobStatus.status} {...jobStatus} />
            ))}
          </CardContent>
        </Card>

        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Latest scraping activities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="h-2 w-2 rounded-full bg-green-500" />
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium">Company data updated</p>
                  <p className="text-xs text-muted-foreground">2 minutes ago</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="h-2 w-2 rounded-full bg-blue-500" />
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium">New scraping job started</p>
                  <p className="text-xs text-muted-foreground">5 minutes ago</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="h-2 w-2 rounded-full bg-yellow-500" />
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium">Job queued for processing</p>
                  <p className="text-xs text-muted-foreground">10 minutes ago</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 