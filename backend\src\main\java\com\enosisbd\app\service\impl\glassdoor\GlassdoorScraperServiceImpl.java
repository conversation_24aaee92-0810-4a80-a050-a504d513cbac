package com.enosisbd.app.service.impl.glassdoor;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.enosisbd.app.model.CompanyDataModel;
import com.enosisbd.app.service.GlassdoorScraperService;
import com.enosisbd.app.service.crawler.HeaderGenerator;
import com.microsoft.playwright.Browser;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.BrowserType;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.WaitUntilState;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class GlassdoorScraperServiceImpl implements GlassdoorScraperService {
    
    @Value("${scraping.timeout-ms:120000}")
    private int TIMEOUT_MS;
    
    @Value("${scraping.glassdoor.login-required:false}")
    private boolean loginRequired;
    
    @Value("${scraping.anti-detection.enabled:true}")
    private boolean antiDetectionEnabled;
    
    @Value("${scraping.glassdoor.page-load-timeout-ms:30000}")
    private int pageLoadTimeoutMs;
    
    @Value("${scraping.glassdoor.ratings-timeout-ms:20000}")
    private int ratingsTimeoutMs;
    
    @Value("${scraping.retry.max-attempts:3}")
    private int maxRetryAttempts;
    
    @Value("${scraping.retry.backoff-multiplier:2000}")
    private long backoffMultiplier;
    
    private final HeaderGenerator headerGenerator;
    
    @Override
    public CompanyDataModel scrapeFromGlassdoor(String companyUrl) {
        log.info("Starting to scrape Glassdoor data for company: {}", companyUrl);
        CompanyDataModel companyDataModel = CompanyDataModel.success("glassdoor");
        
        int maxRetries = maxRetryAttempts;
        int retryCount = 0;
        
        while (retryCount < maxRetries) {
            try {
                log.info("Glassdoor scraping attempt {} of {} for URL: {}", retryCount + 1, maxRetries, companyUrl);
                
                // Use the actual URL parameter instead of hardcoded
                scrapeGlassdoorData(companyUrl, companyDataModel);
                
                // Check if essential fields were captured
                if (companyDataModel.getGlassdoorRating() == null && 
                    companyDataModel.getGlassdoorReviewsCount() == null && 
                    (companyDataModel.getCategoryRatings() == null || companyDataModel.getCategoryRatings().isEmpty())) {
                    throw new RuntimeException("Failed to capture essential Glassdoor data");
                }
                
                log.info("✅ Successfully scraped Glassdoor data for {}", companyUrl);
                companyDataModel.setGlassdoorStatus("SUCCESS");
                return companyDataModel;
                
            } catch (Exception e) {
                retryCount++;
                log.error("Error scraping Glassdoor data for company {} (attempt {} of {}): {}", 
                         companyUrl, retryCount, maxRetries, e.getMessage());
                
                if (retryCount < maxRetries) {
                    try {
                        // Wait before retrying (exponential backoff)
                        long waitTime = backoffMultiplier * retryCount;
                        log.info("Waiting {} ms before retry...", waitTime);
                        Thread.sleep(waitTime);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("Interrupted while waiting for retry");
                        break;
                    }
                } else {
                    log.error("All retry attempts failed for Glassdoor scraping: {}", companyUrl);
                    companyDataModel.setGlassdoorStatus("FAILED");
                    return CompanyDataModel.failed("glassdoor", 
                        String.format("Failed after %d attempts. Last error: %s", maxRetries, e.getMessage()));
                }
            }
        }
        
        companyDataModel.setGlassdoorStatus("FAILED");
        return CompanyDataModel.failed("glassdoor", "Scraping failed after all retry attempts");
    }
    
    private void scrapeGlassdoorData(String url, CompanyDataModel companyDataModel) {
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(new BrowserType.LaunchOptions()
                .setHeadless(!antiDetectionEnabled ? true : false)
            );
            
            BrowserContext context = browser.newContext(new Browser.NewContextOptions()
                    .setUserAgent(headerGenerator.generateHeaders(false, null).get("User-Agent"))
                    .setExtraHTTPHeaders(headerGenerator.generateHeaders(false, "https://www.google.com/")));
            
            context.setDefaultTimeout(TIMEOUT_MS);
            Page page = context.newPage();

            log.info("Navigating to Glassdoor URL: {}", url);
            page.navigate(url, new Page.NavigateOptions()
                .setWaitUntil(WaitUntilState.DOMCONTENTLOADED)
                .setTimeout(pageLoadTimeoutMs)
            );

            page.waitForLoadState(LoadState.DOMCONTENTLOADED);
            log.info("Successfully loaded the Glassdoor page content.");

            // Set profile URL
            companyDataModel.setGlassdoorProfileUrl(url);

            // Extract company name and set as provider name
            try {
                String companyName = page.locator("h1, .employerName, [data-test='employer-name']").first().textContent();
                if (companyName != null && !companyName.trim().isEmpty()) {
                    companyName = companyName.trim();
                    companyDataModel.setName(companyName);
                    companyDataModel.setGlassdoorProviderName(companyName);
                    log.info("Extracted company name: {}", companyName);
                }
            } catch (Exception e) {
                log.warn("Could not extract company name: {}", e.getMessage());
            }

            // Check for verification badge
            try {
                boolean isVerified = page.locator("[data-test='verified-employer'], .verifiedEmployer").isVisible();
                companyDataModel.setGlassdoorVerificationBadge(isVerified);
                log.info("Verification badge status: {}", isVerified);
            } catch (Exception e) {
                log.warn("Could not determine verification status: {}", e.getMessage());
                companyDataModel.setGlassdoorVerificationBadge(false);
            }

            // Extract ratings data
            try {
                log.info("Extracting ratings data...");
                page.waitForSelector("[data-test='industry-average-and-distribution']", 
                    new Page.WaitForSelectorOptions().setTimeout(ratingsTimeoutMs));
                String ratingsHtml = page.locator("[data-test='industry-average-and-distribution']").innerHTML();
                parseRatingsHtml(ratingsHtml, companyDataModel);
                log.info("Successfully extracted ratings data");
            } catch (Exception e) {
                log.warn("Could not extract ratings data: {}", e.getMessage());
            }

            // Extract pros and cons
            try {
                log.info("Extracting pros and cons data...");
                page.waitForSelector(".ReviewHighlights_hightlightContainer__tw9Kl", 
                    new Page.WaitForSelectorOptions().setTimeout(ratingsTimeoutMs));
                String prosConsHtml = page.locator(".ReviewHighlights_hightlightContainer__tw9Kl").innerHTML();
                parseProsConsHtml(prosConsHtml, companyDataModel);
                log.info("Successfully extracted pros and cons data");
            } catch (Exception e) {
                log.warn("Could not extract pros and cons data: {}", e.getMessage());
            }

            // Extract overall rating if available
            try {
                String overallRatingText = page.locator("[data-test='rating'], .rating, .ratingNumber").first().textContent();
                if (overallRatingText != null && !overallRatingText.trim().isEmpty()) {
                    Double rating = Double.parseDouble(overallRatingText.trim());
                    companyDataModel.setGlassdoorRating(rating);
                    log.info("Extracted overall rating: {}", rating);
                }
            } catch (Exception e) {
                log.warn("Could not extract overall rating: {}", e.getMessage());
            }

            // Extract reviews count if available
            try {
                String reviewsText = page.locator("[data-test='reviews-count'], .reviewsCount").first().textContent();
                if (reviewsText != null && !reviewsText.trim().isEmpty()) {
                    String numericPart = reviewsText.replaceAll("[^0-9]", "");
                    if (!numericPart.isEmpty()) {
                        Integer reviewsCount = Integer.parseInt(numericPart);
                        companyDataModel.setGlassdoorReviewsCount(reviewsCount);
                        log.info("Extracted reviews count: {}", reviewsCount);
                    }
                }
            } catch (Exception e) {
                log.warn("Could not extract reviews count: {}", e.getMessage());
            }

            browser.close();
            log.info("Successfully scraped Glassdoor data for {}.", url);
        }
    }

    private CompanyDataModel parseRatingsHtml(String htmlContent, CompanyDataModel companyDataModel) {
        Document doc = Jsoup.parse(htmlContent);

        Map<String, Double> categoryRatings = new LinkedHashMap<>();
        Elements ratingItems = doc.select("a[href*='filter.searchCategory=']");
        log.info("Found {} category rating items using stable href selector.", ratingItems.size());

        for (Element item : ratingItems) {
            try {
                String category = item.text().split("\\d")[0].trim();
                String ratingText = item.text().replaceAll("[^0-9.]", "");
                if (!ratingText.isEmpty()) {
                    Double rating = Double.parseDouble(ratingText);
                    categoryRatings.put(category, rating);

                    // Map specific categories to dedicated fields
                    switch (category.toLowerCase()) {
                        case "career opportunities":
                            companyDataModel.setGlassdoorCareerOpportunities(rating);
                            break;
                        case "compensation and benefits":
                        case "compensation & benefits":
                            companyDataModel.setGlassdoorCompensationBenefits(rating);
                            break;
                        case "culture & values":
                        case "culture and values":
                            companyDataModel.setGlassdoorCultureValues(rating);
                            break;
                        case "diversity & inclusion":
                        case "diversity and inclusion":
                            companyDataModel.setGlassdoorDiversityInclusion(rating);
                            break;
                        case "senior management":
                            companyDataModel.setGlassdoorSeniorManagement(rating);
                            break;
                        case "work/life balance":
                        case "work-life balance":
                            companyDataModel.setGlassdoorWorkLifeBalance(rating);
                            break;
                    }
                }
            } catch (Exception e) {
                log.warn("Failed to parse rating item: {}", e.getMessage());
            }
        }

        companyDataModel.setCategoryRatings(categoryRatings);
        return companyDataModel;
    }

    /**
     * This method is already using the most stable selector available: the 'data-test' attribute.
     * No changes are needed here as this is considered a best practice.
     */
    private CompanyDataModel parseProsConsHtml(String htmlContent, CompanyDataModel companyDataModel) {
        Document doc = Jsoup.parse(htmlContent);
        List<String> pros = new ArrayList<>();
        List<String> cons = new ArrayList<>();

        Element prosContainer = doc.selectFirst("div:contains(Pros)");
        if (prosContainer != null) {
            Elements prosItems = prosContainer.parent().select("li[data-test='review-highlight-text']");
            for (Element item : prosItems) {
                pros.add(item.text().trim());
            }
        }

        Element consContainer = doc.selectFirst("div:contains(Cons)");
        if (consContainer != null) {
            Elements consItems = consContainer.parent().select("li[data-test='review-highlight-text']");
            for (Element item : consItems) {
                cons.add(item.text().trim());
            }
        }

        companyDataModel.setPros(pros);
        companyDataModel.setCons(cons);
        return companyDataModel;
    }
}
