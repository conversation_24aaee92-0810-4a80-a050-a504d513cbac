package com.enosisbd.app.service.crawler;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * Utility class for rotating user agents to avoid detection
 */
@Slf4j
@Component
public class UserAgentRotator {

    private final List<String> desktopUserAgents = new ArrayList<>();
    private final List<String> mobileUserAgents = new ArrayList<>();
    private final Random random = new Random();

    public UserAgentRotator() {
        initializeUserAgents();
    }

    /**
     * Initialize the list of user agents
     */
    private void initializeUserAgents() {
        // Chrome Desktop User Agents
        desktopUserAgents.add("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36");
        desktopUserAgents.add("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36");
        desktopUserAgents.add("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36");
        desktopUserAgents.add("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36");
        desktopUserAgents.add("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36");
        
        // Firefox Desktop User Agents
        desktopUserAgents.add("Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:95.0) Gecko/20100101 Firefox/95.0");
        desktopUserAgents.add("Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:96.0) Gecko/20100101 Firefox/96.0");
        desktopUserAgents.add("Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:95.0) Gecko/20100101 Firefox/95.0");
        
        // Edge Desktop User Agents
        desktopUserAgents.add("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36 Edg/96.0.1054.62");
        desktopUserAgents.add("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36 Edg/97.0.1072.55");
        
        // Safari Desktop User Agents
        desktopUserAgents.add("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Safari/605.1.15");
        desktopUserAgents.add("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.2 Safari/605.1.15");
        
        // Chrome Mobile User Agents
        mobileUserAgents.add("Mozilla/5.0 (iPhone; CPU iPhone OS 15_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/96.0.4664.53 Mobile/15E148 Safari/604.1");
        mobileUserAgents.add("Mozilla/5.0 (iPad; CPU OS 15_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/96.0.4664.53 Mobile/15E148 Safari/604.1");
        mobileUserAgents.add("Mozilla/5.0 (Linux; Android 12; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.104 Mobile Safari/537.36");
        mobileUserAgents.add("Mozilla/5.0 (Linux; Android 12; Pixel 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.104 Mobile Safari/537.36");
        
        // Safari Mobile User Agents
        mobileUserAgents.add("Mozilla/5.0 (iPhone; CPU iPhone OS 15_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Mobile/15E148 Safari/604.1");
        mobileUserAgents.add("Mozilla/5.0 (iPad; CPU OS 15_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Mobile/15E148 Safari/604.1");
        
        // Firefox Mobile User Agents
        mobileUserAgents.add("Mozilla/5.0 (Android 12; Mobile; rv:95.0) Gecko/95.0 Firefox/95.0");
        mobileUserAgents.add("Mozilla/5.0 (iPhone; CPU iPhone OS 15_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) FxiOS/39.0 Mobile/15E148 Safari/605.1.15");
    }

    /**
     * Get a random desktop user agent
     * 
     * @return A random desktop user agent string
     */
    public String getRandomDesktopUserAgent() {
        int index = random.nextInt(desktopUserAgents.size());
        String userAgent = desktopUserAgents.get(index);
        log.debug("Using desktop user agent: {}", userAgent);
        return userAgent;
    }

    /**
     * Get a random mobile user agent
     * 
     * @return A random mobile user agent string
     */
    public String getRandomMobileUserAgent() {
        int index = random.nextInt(mobileUserAgents.size());
        String userAgent = mobileUserAgents.get(index);
        log.debug("Using mobile user agent: {}", userAgent);
        return userAgent;
    }

    /**
     * Get a random user agent (desktop or mobile)
     * 
     * @param preferMobile If true, 70% chance to return a mobile user agent, otherwise 30% chance
     * @return A random user agent string
     */
    public String getRandomUserAgent(boolean preferMobile) {
        if (preferMobile) {
            return random.nextDouble() < 0.7 ? getRandomMobileUserAgent() : getRandomDesktopUserAgent();
        } else {
            return random.nextDouble() < 0.3 ? getRandomMobileUserAgent() : getRandomDesktopUserAgent();
        }
    }
}
