2025-06-30 16:52:31,485 ERROR c.e.a.s.ScrapeScheduledJob [scheduling-1] Error in processScheduledItems job: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]; SQL [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
org.springframework.dao.CannotAcquireLockException: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]; SQL [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:287)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:256)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:244)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:566)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:795)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:758)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:698)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:416)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.enosisbd.app.service.impl.ScrapeSchedulerServiceImpl$$SpringCGLIB$$0.processScheduledItems(<generated>)
	at com.enosisbd.app.scheduler.ScrapeScheduledJob.processScheduledItems(ScrapeScheduledJob.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124)
	at io.micrometer.observation.Observation.observe(Observation.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124)
	at org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.hibernate.exception.LockAcquisitionException: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:108)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:197)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.performNonBatchedMutation(AbstractMutationExecutor.java:134)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorSingleNonBatched.performNonBatchedOperations(MutationExecutorSingleNonBatched.java:55)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.execute(AbstractMutationExecutor.java:55)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.doStaticUpdate(UpdateCoordinatorStandard.java:781)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.performUpdate(UpdateCoordinatorStandard.java:328)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.update(UpdateCoordinatorStandard.java:245)
	at org.hibernate.action.internal.EntityUpdateAction.execute(EntityUpdateAction.java:169)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:644)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:41)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.doFlush(SessionImpl.java:1429)
	at org.hibernate.internal.SessionImpl.managedFlush(SessionImpl.java:491)
	at org.hibernate.internal.SessionImpl.flushBeforeTransactionCompletion(SessionImpl.java:2354)
	at org.hibernate.internal.SessionImpl.beforeTransactionCompletion(SessionImpl.java:1978)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.beforeTransactionCompletion(JdbcCoordinatorImpl.java:439)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl.beforeCompletionCallback(JdbcResourceLocalTransactionCoordinatorImpl.java:169)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl$TransactionDriverControlImpl.commit(JdbcResourceLocalTransactionCoordinatorImpl.java:267)
	at org.hibernate.engine.transaction.internal.TransactionImpl.commit(TransactionImpl.java:101)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:562)
	... 23 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: could not serialize access due to concurrent update
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2734)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2421)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:372)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:518)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:435)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:196)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:157)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:194)
	... 44 common frames omitted
2025-06-30 16:53:28,464 ERROR c.e.a.s.ScrapeScheduledJob [scheduling-1] Error in processScheduledItems job: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]; SQL [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
org.springframework.dao.CannotAcquireLockException: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]; SQL [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:287)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:256)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:244)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:566)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:795)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:758)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:698)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:416)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728)
	at com.enosisbd.app.service.impl.ScrapeSchedulerServiceImpl$$SpringCGLIB$$0.processScheduledItems(<generated>)
	at com.enosisbd.app.scheduler.ScrapeScheduledJob.processScheduledItems(ScrapeScheduledJob.java:48)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:577)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124)
	at io.micrometer.observation.Observation.observe(Observation.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124)
	at org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java:305)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.hibernate.exception.LockAcquisitionException: could not execute statement [ERROR: could not serialize access due to concurrent update] [update companies set all_industries=?,all_services=?,city=?,overall_rating=?,company_description=?,company_logo_url=?,company_overview=?,company_size=?,company_url=?,cost_rating=?,country=?,clutch_employees=?,founded_year=?,clutch_hourly_rate=?,hourly_rate_range=?,industry=?,clutch_location=?,clutch_min_project_size=?,minimum_project_size=?,profile_url=?,provider_name=?,quality_rating=?,clutch_rating=?,clutch_reviews_count=?,schedule_rating=?,slug=?,specialties=?,clutch_status=?,total_reviews=?,verification_badge=?,verification_badge_text=?,verified=?,willing_to_refer=?,created_at=?,data_completeness_score=?,glassdoor_category_ratings=?,glassdoor_cons=?,glassdoor_employee_satisfaction=?,glassdoor_pros=?,glassdoor_rating=?,glassdoor_ratings_distribution=?,glassdoor_reviews_count=?,glassdoor_status=?,goodfirms_client_dislikes=?,goodfirms_client_likes=?,goodfirms_employees=?,goodfirms_location=?,goodfirms_profile_url=?,goodfirms_provider_name=?,goodfirms_rating=?,goodfirms_reviews_count=?,goodfirms_services=?,goodfirms_status=?,goodfirms_verification_badge=?,last_scraped=?,linkedin_employees_count=?,linkedin_followers=?,linkedin_industry=?,linkedin_status=?,name=?,top_mentions=?,updated_at=?,website_achievements=?,website_aliases=?,website_city=?,website_country=?,website_culture_description=?,website_description=?,website_employee_size=?,website_focus_statement=?,website_founded_year=?,website_founding_story=?,website_funding_status=?,website_global_presence=?,website_headquarters_location=?,website_industries=?,website_initiatives=?,website_keywords=?,website_name=?,website_outcome_summary=?,website_service_details=?,website_services=?,website_status=?,website_title=?,website_url=? where id=?]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:108)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:197)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.performNonBatchedMutation(AbstractMutationExecutor.java:134)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorSingleNonBatched.performNonBatchedOperations(MutationExecutorSingleNonBatched.java:55)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.execute(AbstractMutationExecutor.java:55)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.doStaticUpdate(UpdateCoordinatorStandard.java:781)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.performUpdate(UpdateCoordinatorStandard.java:328)
	at org.hibernate.persister.entity.mutation.UpdateCoordinatorStandard.update(UpdateCoordinatorStandard.java:245)
	at org.hibernate.action.internal.EntityUpdateAction.execute(EntityUpdateAction.java:169)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:644)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:511)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:414)
	at org.hibernate.event.internal.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:41)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.doFlush(SessionImpl.java:1429)
	at org.hibernate.internal.SessionImpl.managedFlush(SessionImpl.java:491)
	at org.hibernate.internal.SessionImpl.flushBeforeTransactionCompletion(SessionImpl.java:2354)
	at org.hibernate.internal.SessionImpl.beforeTransactionCompletion(SessionImpl.java:1978)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.beforeTransactionCompletion(JdbcCoordinatorImpl.java:439)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl.beforeCompletionCallback(JdbcResourceLocalTransactionCoordinatorImpl.java:169)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl$TransactionDriverControlImpl.commit(JdbcResourceLocalTransactionCoordinatorImpl.java:267)
	at org.hibernate.engine.transaction.internal.TransactionImpl.commit(TransactionImpl.java:101)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:562)
	... 23 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: could not serialize access due to concurrent update
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2734)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2421)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:372)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:518)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:435)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:196)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:157)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:194)
	... 44 common frames omitted
