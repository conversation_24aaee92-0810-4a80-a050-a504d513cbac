package com.enosisbd.app.gemini;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Jacksonized
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class GeminiResponse {
    @JsonProperty("candidates")
    private List<Candidate> candidates;

    @Data
    @Jacksonized
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Candidate {
        @JsonProperty("content")
        private Content content;
        @JsonProperty("finishReason")
        private String finishReason;
        @JsonProperty("index")
        private Integer index;
    }

    @Data
    @Jacksonized
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Content {
        @JsonProperty("parts")
        private List<Part> parts;
        @JsonProperty("role")
        private String role;
    }

    @Data
    @Jacksonized
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Part {
        @JsonProperty("text")
        private String text;
    }
}