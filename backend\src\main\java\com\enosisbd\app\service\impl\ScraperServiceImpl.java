package com.enosisbd.app.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.enosisbd.app.controller.request.ScrapeRequest;
import com.enosisbd.app.entity.CompanyEntity;
import com.enosisbd.app.entity.ScrapeEntity;
import com.enosisbd.app.entity.ScrapeStatus;
import com.enosisbd.app.repository.CompanyRepository;
import com.enosisbd.app.repository.ScrapeRepository;
import com.enosisbd.app.service.ScrapeSchedulerService;
import com.enosisbd.app.service.ScraperService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ScraperServiceImpl implements ScraperService {

    private final ScrapeRepository scrapeRepository;
    private final CompanyRepository companyRepository;
    private final ScrapeSchedulerService scrapeSchedulerService;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.SERIALIZABLE)
    public void saveScrapeRequest(ScrapeRequest scrapeRequest) {
        log.info("Saving scrape request for: {}", scrapeRequest.getName());

        // Create or find existing company
        CompanyEntity companyEntity = createOrFindCompany(scrapeRequest.getName());
        
        // Create scrape entity
        ScrapeEntity scrapeEntity = createScrapeEntity(scrapeRequest, companyEntity);

        // Calculate initial scheduled time with some randomization
        scrapeSchedulerService.calculateAndSetNextScheduledTime(scrapeEntity);
        
        scrapeRepository.save(scrapeEntity);
        
        log.info("Scrape request saved with ID: {} for company: {} and scheduled for: {}", 
                scrapeEntity.getId(), companyEntity.getName(), scrapeEntity.getNextScheduledTime());
    }

    /**
     * Create or find existing company by name
     */
    private CompanyEntity createOrFindCompany(String companyName) {
        return companyRepository.findByNameIgnoreCase(companyName)
            .orElseGet(() -> {
                log.info("Creating new company: {}", companyName);
                CompanyEntity newCompanyEntity = new CompanyEntity();
                newCompanyEntity.setName(companyName);
                newCompanyEntity.setCreatedAt(LocalDateTime.now());
                return companyRepository.save(newCompanyEntity);
            });
    }

    /**
     * Create ScrapeEntity from ScrapeRequest
     */
    private ScrapeEntity createScrapeEntity(ScrapeRequest scrapeRequest, CompanyEntity companyEntity) {
        ScrapeEntity scrapeEntity = new ScrapeEntity();
        
        // Map fields from request to entity
        scrapeEntity.setName(scrapeRequest.getName());
        scrapeEntity.setGlassdoor(scrapeRequest.getGlassdoor());
        scrapeEntity.setClutch(scrapeRequest.getClutch());
        scrapeEntity.setGoodfirms(scrapeRequest.getGoodfirms());
        scrapeEntity.setWebsite(scrapeRequest.getWebsite());
        scrapeEntity.setLinkedin(scrapeRequest.getLinkedin());
        scrapeEntity.setStatus(ScrapeStatus.QUEUE);
        scrapeEntity.setCsvFileName(scrapeRequest.getCsvFileName());
        scrapeEntity.setCsvUploadId(scrapeRequest.getCsvUploadId());
        
        // Set initial status and scheduling info
        scrapeEntity.setRetryCount(0);
        scrapeEntity.setMaxRetries(3);
        
        // Associate with company
        scrapeEntity.setCompanyEntity(companyEntity);
        
        return scrapeEntity;
    }

    @Override
    @Transactional(readOnly = true)
    public List<ScrapeEntity> getAllQueuedScrapes() {
        log.info("Fetching all queued scrape entities");
        return scrapeRepository.findAllQueuedScrapes();
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<ScrapeEntity> getAllScrapesByStatus(ScrapeStatus status) {
        log.info("Fetching all scrape entities with status: {}", status);
        return scrapeRepository.findByStatus(status);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CompanyEntity> getAllCompanies() {
        log.info("Fetching all companies");
        return companyRepository.findAll();
    }
    
    @Override
    @Transactional(readOnly = true)
    public Optional<CompanyEntity> getCompanyById(Long id) {
        log.info("Fetching company with ID: {}", id);
        return companyRepository.findById(id);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<CompanyEntity> getCompaniesByCompleteness(Double minScore) {
        log.info("Fetching companies with completeness score >= {}", minScore);
        return null;
    }

    @Override
    public List<Map<String, Object>> getCsvUploads() {
        List<Object[]> results = scrapeRepository.findCsvUploads();
        List<Map<String, Object>> uploads = new ArrayList<>();

        for (Object[] result : results) {
            Map<String, Object> upload = new HashMap<>();
            upload.put("id", result[0]);
            upload.put("fileName", result[1]);
            upload.put("uploadedAt", result[2]);
            upload.put("totalJobs", result[3]);
            upload.put("completedJobs", result[4]);
            upload.put("failedJobs", result[5]);
            upload.put("inProgressJobs", result[6]);
            uploads.add(upload);
        }

        return uploads;
    }

    @Override
    public List<ScrapeEntity> getScrapesByCsvUploadId(String csvUploadId) {
        return scrapeRepository.findByCsvUploadId(csvUploadId);
    }
}
