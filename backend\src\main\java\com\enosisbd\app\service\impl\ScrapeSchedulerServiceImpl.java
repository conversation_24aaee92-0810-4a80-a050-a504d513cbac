package com.enosisbd.app.service.impl;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

import org.flowable.engine.RuntimeService;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.enosisbd.app.config.SchedulingConfig;
import com.enosisbd.app.entity.ScrapeEntity;
import com.enosisbd.app.entity.ScrapeStatus;
import com.enosisbd.app.repository.ScrapeRepository;
import com.enosisbd.app.service.ScrapeSchedulerService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ScrapeSchedulerServiceImpl implements ScrapeSchedulerService {

    private final ScrapeRepository scrapeRepository;
    private final RuntimeService runtimeService;
    private final SchedulingConfig schedulingConfig;
    private final RetryTemplate retryTemplate;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.SERIALIZABLE)
    public void scheduleReadyItems() {
        log.debug("Starting to schedule ready items...");
        
        LocalDateTime currentTime = LocalDateTime.now();
        List<ScrapeEntity> readyItems = scrapeRepository.findItemsReadyForScheduling(currentTime);
        
        log.debug("Current time: {}, Found {} items ready for scheduling", currentTime, readyItems.size());
        
        if (readyItems.isEmpty()) {
            // Check if there are any items in QUEUE status at all
            List<ScrapeEntity> queueItems = scrapeRepository.findByStatus(ScrapeStatus.QUEUE);
            log.debug("Total items in QUEUE status: {}", queueItems.size());
            
            if (!queueItems.isEmpty()) {
                log.debug("Queue items exist but not ready for scheduling:");
                queueItems.forEach(item -> 
                    log.debug("Item ID: {}, Name: {}, Next Scheduled Time: {}", 
                        item.getId(), item.getName(), item.getNextScheduledTime())
                );
            }
            
            log.debug("No items ready for scheduling");
            return;
        }
        
        log.info("Found {} items ready for scheduling", readyItems.size());
        
        // Check if we can schedule new items (no conflicts with processing items)
        List<ScrapeEntity> processingItems = scrapeRepository.findByStatus(ScrapeStatus.IN_PROGRESS);
        if (processingItems.size() >= schedulingConfig.getMaxConcurrentProcessing()) {
            log.info("Maximum concurrent processing limit reached. Currently processing: {}. Skipping scheduling.", processingItems.size());
            return;
        }
        
        // Calculate next available scheduling slot
        LocalDateTime nextAvailableSlot = calculateNextAvailableSlot();
        log.debug("Next available slot calculated as: {}", nextAvailableSlot);
        
        for (ScrapeEntity entity : readyItems) {
            try {
                LocalDateTime oldScheduledTime = entity.getNextScheduledTime();
                
                // Schedule each item with incremental time slots
                entity.setNextScheduledTime(nextAvailableSlot);
                entity.setStatus(ScrapeStatus.SCHEDULED);
                entity.setLastModifiedBy("SCHEDULER");
                
                scrapeRepository.save(entity);
                
                log.info("Scheduled scrape entity {} (name: {}) from {} to {}", 
                    entity.getId(), entity.getName(), oldScheduledTime, nextAvailableSlot);
                
                // Add random gap for next item
                int randomGap = schedulingConfig.getMinGapMinutes() + 
                    ThreadLocalRandom.current().nextInt(schedulingConfig.getMaxAdditionalRandomMinutes());
                nextAvailableSlot = nextAvailableSlot.plusMinutes(randomGap);
                
                log.debug("Next item will be scheduled for: {} (gap: {} minutes)", nextAvailableSlot, randomGap);
                
            } catch (Exception e) {
                log.error("Failed to schedule entity {}: {}", entity.getId(), e.getMessage(), e);
            }
        }
        
        log.info("Completed scheduling {} items", readyItems.size());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.SERIALIZABLE)
    public void processScheduledItems() {
        log.debug("Starting to process scheduled items...");
        
        LocalDateTime currentTime = LocalDateTime.now();
        List<ScrapeEntity> readyForProcessing = scrapeRepository.findItemsReadyForProcessing(currentTime);
        
        log.debug("Current time: {}, Found {} items ready for processing", currentTime, readyForProcessing.size());
        
        if (readyForProcessing.isEmpty()) {
            // Check if there are any scheduled items at all
            List<ScrapeEntity> scheduledItems = scrapeRepository.findByStatus(ScrapeStatus.SCHEDULED);
            log.debug("Total items in SCHEDULED status: {}", scheduledItems.size());
            
            if (!scheduledItems.isEmpty()) {
                log.debug("Scheduled items exist but not ready for processing:");
                scheduledItems.forEach(item -> 
                    log.debug("Item ID: {}, Name: {}, Scheduled Time: {}, Time until ready: {} minutes", 
                        item.getId(), item.getName(), item.getNextScheduledTime(),
                        java.time.Duration.between(currentTime, item.getNextScheduledTime()).toMinutes())
                );
            }
            
            log.debug("No items ready for processing");
            return;
        }
        
        // Check current processing limit
        List<ScrapeEntity> currentlyProcessing = scrapeRepository.findByStatus(ScrapeStatus.IN_PROGRESS);
        int availableSlots = schedulingConfig.getMaxConcurrentProcessing() - currentlyProcessing.size();
        
        log.debug("Currently processing: {}, Available slots: {}", currentlyProcessing.size(), availableSlots);
        
        if (availableSlots <= 0) {
            log.info("No available processing slots. Currently processing: {}", currentlyProcessing.size());
            return;
        }
        
        // Process items up to available slots
        int itemsToProcess = Math.min(availableSlots, readyForProcessing.size());
        List<ScrapeEntity> itemsForProcessing = readyForProcessing.subList(0, itemsToProcess);
        
        log.info("Processing {} items", itemsForProcessing.size());
        
        for (ScrapeEntity entity : itemsForProcessing) {
            try {
                log.info("Starting to process entity {} (name: {})", entity.getId(), entity.getName());
                startProcessing(entity);
            } catch (Exception e) {
                log.error("Failed to start processing entity {}: {}", entity.getId(), e.getMessage());
                markAsFailed(entity, "Failed to start processing: " + e.getMessage());
            }
        }
    }

    @Override
    public void calculateAndSetNextScheduledTime(ScrapeEntity entity) {
        LocalDateTime nextSlot = calculateNextAvailableSlot();
        entity.setNextScheduledTime(nextSlot);
        log.debug("Calculated next scheduled time for entity {} (name: {}): {}", entity.getId(), entity.getName(), nextSlot);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void startProcessing(ScrapeEntity entity) {
        log.info("Starting processing for scrape entity: {} (name: {})", entity.getId(), entity.getName());
        
        retryTemplate.execute(context -> {
            entity.setStatus(ScrapeStatus.IN_PROGRESS);
            entity.setLastProcessedTime(LocalDateTime.now());
            entity.setLastModifiedBy("PROCESSOR");
            
            scrapeRepository.save(entity);
            
            // Start Flowable workflow process
            try {
                Map<String, Object> processVariables = new HashMap<>();
                processVariables.put("scrapeEntityId", entity.getId());
                processVariables.put("name", entity.getName());
                processVariables.put("glassdoor", entity.getGlassdoor());
                processVariables.put("clutch", entity.getClutch());
                processVariables.put("goodfirms", entity.getGoodfirms());
                processVariables.put("website", entity.getWebsite());
                processVariables.put("linkedin", entity.getLinkedin());
                
                String processInstanceId = runtimeService.startProcessInstanceByKey("dataPuller", processVariables).getId();
                
                log.info("Started Flowable process {} for scrape entity {} (name: {})", 
                    processInstanceId, entity.getId(), entity.getName());
                
            } catch (Exception e) {
                log.error("Failed to start Flowable process for entity {}: {}", entity.getId(), e.getMessage());
                markAsFailed(entity, "Failed to start workflow: " + e.getMessage());
            }
            return null;
        });
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void markAsCompleted(ScrapeEntity entity) {
        log.info("Marking scrape entity {} (name: {}) as completed", entity.getId(), entity.getName());
        
        entity.setStatus(ScrapeStatus.SUCCESS);
        entity.setLastModifiedBy("PROCESSOR");
        entity.setRetryCount(0); // Reset retry count on success
        
        scrapeRepository.save(entity);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void markAsFailed(ScrapeEntity entity, String error) {
        log.warn("Marking scrape entity {} (name: {}) as failed: {}", entity.getId(), entity.getName(), error);
        
        entity.setRetryCount(entity.getRetryCount() + 1);
        entity.setLastModifiedBy("PROCESSOR");
        
        if (entity.getRetryCount() >= entity.getMaxRetries()) {
            // Max retries reached, mark as permanently failed
            entity.setStatus(ScrapeStatus.FAILED);
            log.error("Scrape entity {} (name: {}) has reached maximum retries and is marked as FAILED", 
                entity.getId(), entity.getName());
        } else {
            // Schedule for retry with exponential backoff
            entity.setStatus(ScrapeStatus.QUEUE);
            LocalDateTime nextRetryTime = LocalDateTime.now()
                .plusMinutes(schedulingConfig.getMinGapMinutes() * (long) Math.pow(2, entity.getRetryCount()));
            entity.setNextScheduledTime(nextRetryTime);
            
            log.info("Scheduled retry {} for entity {} (name: {}) at {}", 
                entity.getRetryCount(), entity.getId(), entity.getName(), nextRetryTime);
        }
        
        scrapeRepository.save(entity);
    }

    /**
     * Calculate the next available time slot considering minimum gaps and existing schedules
     */
    private LocalDateTime calculateNextAvailableSlot() {
        LocalDateTime currentTime = LocalDateTime.now();
        
        // Find the most recent processed item
        return scrapeRepository.findMostRecentlyProcessedItem()
            .map(recentItem -> {
                LocalDateTime lastProcessedTime = recentItem.getLastProcessedTime();
                if (lastProcessedTime != null) {
                    LocalDateTime minNextTime = lastProcessedTime.plusMinutes(schedulingConfig.getMinGapMinutes());
                    // If the minimum next time is in the future, use it; otherwise use current time
                    LocalDateTime baseTime = minNextTime.isAfter(currentTime) ? minNextTime : currentTime;
                    // Add random additional minutes
                    int randomMinutes = ThreadLocalRandom.current().nextInt(schedulingConfig.getMaxAdditionalRandomMinutes());
                    LocalDateTime result = baseTime.plusMinutes(randomMinutes);
                    
                    log.debug("Calculated slot based on recent item: lastProcessed={}, minNext={}, base={}, random={}, result={}", 
                        lastProcessedTime, minNextTime, baseTime, randomMinutes, result);
                    
                    return result;
                }
                LocalDateTime result = currentTime.plusMinutes(ThreadLocalRandom.current().nextInt(1, 3)); // 1-3 minutes from now
                log.debug("No last processed time, scheduling for: {}", result);
                return result;
            })
            .orElse(currentTime.plusMinutes(ThreadLocalRandom.current().nextInt(1, 3))); // 1-3 minutes from now if no previous items
    }
} 