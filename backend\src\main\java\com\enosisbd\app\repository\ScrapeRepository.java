package com.enosisbd.app.repository;

import com.enosisbd.app.entity.ScrapeEntity;
import com.enosisbd.app.entity.ScrapeStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ScrapeRepository extends JpaRepository<ScrapeEntity, Long> {
    
    /**
     * Find all scrape entities with specific status
     * @param status The status to filter by
     * @return List of ScrapeEntity with given status
     */
    List<ScrapeEntity> findByStatus(ScrapeStatus status);
    
    /**
     * Find all scrape entities with QUEUE status - convenience method
     * @return List of ScrapeEntity with QUEUE status
     */
    default List<ScrapeEntity> findAllQueuedScrapes() {
        return findByStatus(ScrapeStatus.QUEUE);
    }
    
    /**
     * Find items that are ready to be scheduled (QUEUE status and no scheduled time or scheduled time is past)
     * @param currentTime Current time to compare against
     * @return List of items ready for scheduling
     */
    @Query("SELECT s FROM ScrapeEntity s WHERE s.status = 'QUEUE' AND (s.nextScheduledTime IS NULL OR s.nextScheduledTime <= :currentTime) ORDER BY s.createdAt ASC")
    List<ScrapeEntity> findItemsReadyForScheduling(@Param("currentTime") LocalDateTime currentTime);
    
    /**
     * Find items that are scheduled and ready to be processed
     * @param currentTime Current time to compare against
     * @return List of items ready for processing
     */
    @Query("SELECT s FROM ScrapeEntity s WHERE s.status = 'SCHEDULED' AND s.nextScheduledTime <= :currentTime ORDER BY s.nextScheduledTime ASC")
    List<ScrapeEntity> findItemsReadyForProcessing(@Param("currentTime") LocalDateTime currentTime);
    
    /**
     * Find the most recent processed item to calculate next scheduling gap
     * @return Optional of the most recently processed item
     */
    @Query("SELECT s FROM ScrapeEntity s WHERE s.lastProcessedTime IS NOT NULL ORDER BY s.lastProcessedTime DESC LIMIT 1")
    Optional<ScrapeEntity> findMostRecentlyProcessedItem();
    
    /**
     * Find all currently processing items
     * @return List of items currently being processed
     */
    List<ScrapeEntity> findByStatusAndLastProcessedTimeIsNotNull(ScrapeStatus status);

    List<ScrapeEntity> findByCsvUploadId(String csvUploadId);

    @Query(value = """
        SELECT 
            csv_upload_id as id,
            csv_file_name as fileName,
            MIN(created_at) as uploadedAt,
            COUNT(*) as totalJobs,
            COUNT(CASE WHEN status = 'SUCCESS' THEN 1 END) as completedJobs,
            COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failedJobs,
            COUNT(CASE WHEN status = 'IN_PROGRESS' THEN 1 END) as inProgressJobs
        FROM scrape_entity
        WHERE csv_upload_id IS NOT NULL
        GROUP BY csv_upload_id, csv_file_name
        ORDER BY MIN(created_at) DESC
    """, nativeQuery = true)
    List<Object[]> findCsvUploads();
}
