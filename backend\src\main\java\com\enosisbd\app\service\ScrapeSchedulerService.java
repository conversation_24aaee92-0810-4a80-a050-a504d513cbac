package com.enosisbd.app.service;

import com.enosisbd.app.entity.ScrapeEntity;

/**
 * Service interface for managing scrape scheduling operations
 */
public interface ScrapeSchedulerService {
    
    /**
     * Process items that are ready for scheduling
     * This method schedules QUEUE items with proper timing gaps
     */
    void scheduleReadyItems();
    
    /**
     * Process items that are scheduled and ready to be scraped
     * This method starts the actual scraping process for scheduled items
     */
    void processScheduledItems();
    
    /**
     * Calculate the next scheduled time for an item
     * @param entity The scrape entity to schedule
     * @return The calculated next scheduled time
     */
    void calculateAndSetNextScheduledTime(ScrapeEntity entity);
    
    /**
     * Start processing a scrape entity
     * @param entity The entity to process
     */
    void startProcessing(ScrapeEntity entity);
    
    /**
     * Mark a scrape entity as completed successfully
     * @param entity The entity that completed successfully
     */
    void markAsCompleted(ScrapeEntity entity);
    
    /**
     * Mark a scrape entity as failed and schedule retry if applicable
     * @param entity The entity that failed
     * @param error The error message
     */
    void markAsFailed(ScrapeEntity entity, String error);
} 