spring:
  main:
    banner-mode: OFF
  ai:
    ollama:
      base-url: http://*************:11434
  profiles:
    active:
      - dev
  application:
    name: EOS Data Scraper
  datasource:
    url: ${SPRING_DATASOURCE_URL:************************************}
    username: ${SPRING_DATASOURCE_USERNAME:postgres}
    password: ${SPRING_DATASOURCE_PASSWORD:root}
    driver-class-name: org.postgresql.Driver
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: create-drop
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.PostgreSQLDialect

# Scraping Scheduler Configuration (Fast settings for testing)
scraping:
  # Scraping timeout configuration
  timeout-ms: 120000

  # Goodfirms-specific configuration
  goodfirms:
    login-required: false
    # Specific timeouts for different Goodfirms operations
    basic-data-timeout-ms: 30000
    logo-timeout-ms: 15000
    feedback-timeout-ms: 45000
    page-load-timeout-ms: 20000

  # Glassdoor-specific configuration
  glassdoor:
    login-required: false
    # Specific timeouts for different Glassdoor operations
    page-load-timeout-ms: 30000
    ratings-timeout-ms: 20000
    pros-cons-timeout-ms: 25000
    overall-data-timeout-ms: 35000

  # Anti-detection configuration
  anti-detection:
    enabled: true
    random-delays: true
    min-delay-ms: 500
    max-delay-ms: 3000

  # General scraping timeout
  timeout: 30000

  # Retry configuration for database operations
  retry:
    max-attempts: 5
    initial-interval: 500
    multiplier: 2.0
    max-interval: 5000

  # Page wait times
  wait-times:
    default-wait-ms: 5000
    review-section-wait-ms: 10000
    javascript-load-wait-ms: 8000

  scheduler:
    # Minimum gap between scraping operations in minutes (reduced for testing)
    min-gap-minutes: 1
    # Maximum additional random minutes to add to the minimum gap (reduced for testing)
    max-additional-random-minutes: 2
    # Maximum number of concurrent scraping operations
    max-concurrent-processing: 1
    # Default maximum retry attempts for failed scraping operations
    default-max-retries: 3
    # Job scheduling intervals
    scheduling-job-interval-minutes: 1
    processing-job-interval-minutes: 1
    maintenance-job-interval-minutes: 5
    # Initial delays for jobs
    scheduling-job-initial-delay-minutes: 1
    processing-job-initial-delay-minutes: 1
    maintenance-job-initial-delay-minutes: 1

# Flowable Configuration
flowable:
  async-executor-activate: true
  database-schema-update: true

# File Watcher Configuration
file-watcher:
  input-folder: ${FILE_WATCHER_INPUT_FOLDER:./input}
  output-folder: ${FILE_WATCHER_OUTPUT_FOLDER:./output}
  processed-folder: ${FILE_WATCHER_PROCESSED_FOLDER:./processed}
  error-folder: ${FILE_WATCHER_ERROR_FOLDER:./error}
  polling-interval-seconds: 5
  enabled: true
  supported-extensions:
    - json
  auto-create-folders: true
  # Processing configuration
  processing-timeout-minutes: 1
  large-file-threshold-mb: 5
  disable-auto-commit-for-large-files: true
  max-concurrent-files: 3

# Logging Configuration
logging:
  level:
    com.enosisbd.app: DEBUG
    org.flowable: INFO
    org.springframework.scheduling: DEBUG
