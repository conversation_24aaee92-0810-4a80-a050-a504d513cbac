package com.enosisbd.app.service;

import com.enosisbd.app.entity.CompanyEntity;
import com.enosisbd.app.entity.ScrapeEntity;
import com.enosisbd.app.entity.ScrapeStatus;
import com.enosisbd.app.controller.request.ScrapeRequest;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface ScraperService {
    void saveScrapeRequest(ScrapeRequest scrapeRequest);
    
    /**
     * Get all scrape entities with QUEUE status
     * @return List of ScrapeEntity with QUEUE status
     */
    List<ScrapeEntity> getAllQueuedScrapes();
    
    /**
     * Get all scrape entities by status
     * @param status The status to filter by
     * @return List of ScrapeEntity with given status
     */
    List<ScrapeEntity> getAllScrapesByStatus(ScrapeStatus status);
    
    /**
     * Get all companies
     * @return List of all companies
     */
    List<CompanyEntity> getAllCompanies();
    
    /**
     * Get company by ID
     * @param id Company ID
     * @return Optional Company
     */
    Optional<CompanyEntity> getCompanyById(Long id);
    
    /**
     * Get companies by data completeness score
     * @param minScore Minimum completeness score
     * @return List of companies with score >= minScore
     */
    List<CompanyEntity> getCompaniesByCompleteness(Double minScore);

    List<Map<String, Object>> getCsvUploads();

    List<ScrapeEntity> getScrapesByCsvUploadId(String csvUploadId);
}
