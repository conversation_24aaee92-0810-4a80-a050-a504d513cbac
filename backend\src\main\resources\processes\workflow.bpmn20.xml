<?xml version="1.0" encoding="UTF-8"?>
<definitions
        xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
        targetNamespace="EnhancedProcess" xmlns:flowable="http://flowable.org/bpmn">

    <process id="dataPuller" name="Structured Data Puller">
        <!-- Start of the process -->
        <startEvent id="startEvent" name="Start ETL Process"/>
        <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="extractData"/>

        <!-- Data Extraction Task -->
        <serviceTask id="extractData" name="Extract Data"
                     flowable:delegateExpression="${dataExtractionDelegate}"/>
        <sequenceFlow id="flow2" sourceRef="extractData" targetRef="storeData"/>

        <!-- Data Storage Task -->
        <serviceTask id="storeData" name="Store Data"
                     flowable:delegateExpression="${databaseStorageDelegate}"/>
        <sequenceFlow id="flow5" sourceRef="storeData" targetRef="endEvent"/>

        <!-- End of the process -->
        <endEvent id="endEvent" name="ETL Process Completed"/>
    </process>

</definitions>