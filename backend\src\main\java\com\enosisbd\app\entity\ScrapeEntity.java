package com.enosisbd.app.entity;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.proxy.HibernateProxy;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.Objects;

@Getter
@Setter
@Entity
@ToString
@AllArgsConstructor
@RequiredArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class ScrapeEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;
    @Column(name = "name")
    private String name;
    @Column(name = "glassdoor")
    private String glassdoor;
    @Column(name = "clutch")
    private String clutch;
    @Column(name = "goodfirms")
    private String goodfirms;
    @Column(name = "website")
    private String website;
    @Column(name = "linkedin")
    private String linkedin;
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private ScrapeStatus status;
    @Column(name = "next_scheduled_time")
    private LocalDateTime nextScheduledTime;
    @Column(name = "last_processed_time")
    private LocalDateTime lastProcessedTime;
    @Column(name = "retry_count")
    private Integer retryCount = 0;
    @Column(name = "max_retries")
    private Integer maxRetries = 3;

    @Column(name = "csv_file_name")
    private String csvFileName;
    
    @Column(name = "csv_upload_id")
    private String csvUploadId;

    @OneToOne(fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    @JoinColumn(name = "company_id")
    private CompanyEntity companyEntity;

    @CreatedDate
    private LocalDateTime createdAt;
    @LastModifiedDate
    private LocalDateTime updatedAt;
    @CreatedBy
    private String createdBy;
    @LastModifiedBy
    private String lastModifiedBy;

    @Override
    public final boolean equals(Object o) {
        if (this == o) return true;
        if (o == null) return false;
        Class<?> oEffectiveClass = o instanceof HibernateProxy ? ((HibernateProxy) o).getHibernateLazyInitializer().getPersistentClass() : o.getClass();
        Class<?> thisEffectiveClass = this instanceof HibernateProxy ? ((HibernateProxy) this).getHibernateLazyInitializer().getPersistentClass() : this.getClass();
        if (thisEffectiveClass != oEffectiveClass) return false;
        ScrapeEntity that = (ScrapeEntity) o;
        return getId() != null && Objects.equals(getId(), that.getId());
    }

    @Override
    public final int hashCode() {
        return this instanceof HibernateProxy ? ((HibernateProxy) this).getHibernateLazyInitializer().getPersistentClass().hashCode() : getClass().hashCode();
    }
}
