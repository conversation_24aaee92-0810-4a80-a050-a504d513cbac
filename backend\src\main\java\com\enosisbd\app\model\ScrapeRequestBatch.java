package com.enosisbd.app.model;

import com.enosisbd.app.controller.request.ScrapeRequest;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScrapeRequestBatch {
    
    private String batchId;
    private String description;
    private LocalDateTime createdAt;
    private List<ScrapeRequest> scrapeRequests;
    private Integer totalRequests;
    
    public ScrapeRequestBatch(List<ScrapeRequest> scrapeRequests) {
        this.scrapeRequests = scrapeRequests;
        this.totalRequests = scrapeRequests != null ? scrapeRequests.size() : 0;
        this.createdAt = LocalDateTime.now();
    }
    
    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public int getTotalRequests() {
        return totalRequests != null ? totalRequests : (scrapeRequests != null ? scrapeRequests.size() : 0);
    }
} 