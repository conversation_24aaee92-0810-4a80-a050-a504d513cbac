package com.enosisbd.app.util;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Utility class for extracting provider website URLs from Clutch.co redirect URLs
 */
public class ClutchUrlExtractor {

    // Pattern to match the provider_website parameter in the URL
    private static final Pattern PROVIDER_WEBSITE_PATTERN = Pattern.compile("provider_website=([^&]+)");
    
    // Pattern to match the u= parameter which contains the full website URL
    private static final Pattern U_PARAM_PATTERN = Pattern.compile("u=([^&]+)");
    
    /**
     * Extracts the provider website URL from a Clutch redirect URL
     * 
     * @param clutchRedirectUrl The Clutch redirect URL containing the provider website
     * @return The extracted provider website URL or null if not found
     */
    public static String extractProviderWebsite(String clutchRedirectUrl) {
        if (clutchRedirectUrl == null || clutchRedirectUrl.isEmpty()) {
            return null;
        }
        
        // First try to extract from provider_website parameter
        String providerWebsite = extractFromPattern(clutchRedirectUrl, PROVIDER_WEBSITE_PATTERN);
        if (providerWebsite != null) {
            return providerWebsite;
        }
        
        // If not found, try to extract from u= parameter
        return extractFromPattern(clutchRedirectUrl, U_PARAM_PATTERN);
    }
    
    /**
     * Extracts and decodes a URL parameter based on the provided pattern
     * 
     * @param url The URL to extract from
     * @param pattern The regex pattern to match
     * @return The decoded parameter value or null if not found
     */
    private static String extractFromPattern(String url, Pattern pattern) {
        Matcher matcher = pattern.matcher(url);
        if (matcher.find()) {
            String encodedUrl = matcher.group(1);
            try {
                // URL decode the extracted website
                return URLDecoder.decode(encodedUrl, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                // This should never happen with UTF-8
                return encodedUrl;
            }
        }
        return null;
    }
    
    /**
     * Normalizes a website URL by ensuring it has a proper protocol prefix
     * 
     * @param websiteUrl The website URL to normalize
     * @return The normalized URL
     */
    public static String normalizeWebsiteUrl(String websiteUrl) {
        if (websiteUrl == null || websiteUrl.isEmpty()) {
            return null;
        }
        
        // If the URL doesn't start with http:// or https://, add https://
        if (!websiteUrl.startsWith("http://") && !websiteUrl.startsWith("https://")) {
            return "https://" + websiteUrl;
        }
        
        return websiteUrl;
    }
}
