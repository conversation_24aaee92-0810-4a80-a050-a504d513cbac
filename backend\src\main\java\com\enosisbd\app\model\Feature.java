package com.enosisbd.app.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

// --- Feature ---
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Feature {
    private UUID featureId;
    private String name;
    private String description;

    // For Many-to-Many relationship with ProductService (managed through ProductServiceFeature table)
    // In a real JPA/Hibernate setup, this would often be mapped with @ManyToMany
    private List<ProductService> productServices = new ArrayList<>();
}
